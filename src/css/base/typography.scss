html {
	color: $colorText;
	font-family: $fontPrimary;
	font-size: 14px;
	line-height: (24/14);
	letter-spacing: 1px;
	@media ($mdUp) {
		font-size: 16px;
		line-height: (26/16);
	}
	@media ($lgUp) {
		font-size: $fontSize;
		line-height: $lineHeight;
	}
}

// Headings
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6,
.display,
.subtitle {
	margin: 1.5em 0 0.5em;
	font-weight: normal;
	line-height: 1.2;
}
h1,
.h1 {
	font-size: 40px;
	line-height: (60/40);
	@media ($mdUp) {
		font-size: 60px;
		line-height: (72/60);
	}
	@media ($lgUp) {
		font-weight: 300;
		font-size: 90px;
	}
}
h2,
.h2 {
	font-size: 32px;
	line-height: (38/32);
	@media ($mdUp) {
		font-size: 40px;
		line-height: (60/40);
	}
	@media ($lgUp) {
		font-weight: 300;
		font-size: 60px;
	}
}
h3,
.h3 {
	font-size: 30px;
	line-height: (36/30);
	@media ($mdUp) {
		font-size: 32px;
		line-height: (38/32);
	}
	@media ($lgUp) {
		font-weight: 300;
		font-size: 40px;
		line-height: (60/40);
	}
}
h4,
.h4 {
	font-size: 16px;
	line-height: (26/16);
	@media ($mdUp) {
		font-size: 24px;
		line-height: (34/24);
	}
	@media ($lgUp) {
		font-weight: 300;
		font-size: 30px;
		line-height: (36/30);
	}
}
h5,
.h5 {
	font-size: 16px;
	line-height: (26/16);
	@media ($lgUp) {
		font-size: 20px;
		line-height: (32/20);
	}
}
h6,
.h6 {
	font-size: 14px;
	line-height: 1.4;
}
.subtitle {
	font-weight: 600;
	font-size: 13px;
	line-height: (19/13);
	text-transform: uppercase;
}
.display {
	font-size: 80px;
	@media ($mdUp) {
		font-size: 110px;
	}
	@media ($lgUp) {
		font-weight: 300;
		font-size: 160px;
	}
}

// Paragraph
p {
	margin: 0 0 $typoSpaceVertical;
}
hr {
	height: 1px;
	margin: $typoSpaceVertical 0;
	border: solid $colorBd;
	border-width: 1px 0 0;
	overflow: hidden;
}

// Blockquote
blockquote {
	margin: 0 0 $typoSpaceVertical;
	padding: 0;
	> :last-child {
		margin-bottom: 0;
	}
}

// Links
a,
.as-link {
	color: $colorLink;
	transition: color $t;
	cursor: pointer;
	.hoverevents &:hover {
		color: $colorHover;
	}
}

a,
.a,
.as-link {
	text-decoration: underline;
	text-underline-offset: 0.35em;
	text-decoration-thickness: 1px;
	-webkit-tap-highlight-color: transparent;
}

// Lists
ul,
ol,
dl {
	margin: 0 0 $typoSpaceVertical;
	padding: 0;
	list-style: none;
	& & {
		margin-top: ($typoSpaceVertical / 4);
	}
}
li {
	margin: 0 0 ($typoSpaceVertical / 4);
	padding: 0 0 0 30px;
}
ul {
	li {
		background-image: url($svgBullet);
		background-position: 0 0.65em;
		background-repeat: no-repeat;
		background-size: 4px 4px;
	}
}
ol {
	counter-reset: item;
	li {
		position: relative;
		&::before {
			content: counter(item) '.';
			counter-increment: item;
			position: absolute;
			top: 0;
			left: 0;
		}
	}
	ol {
		li {
			&::before {
				content: counter(item, lower-alpha) '.';
			}
		}
	}
}
dt {
	margin: 0;
	font-weight: bold;
}
dd {
	margin: 0 0 ($typoSpaceVertical / 2);
	padding: 0;
}

// Tables
table {
	clear: both;
	border-collapse: collapse;
	border-spacing: 0;
	empty-cells: show;
	width: 100%;
	margin: 0 0 $typoSpaceVertical;
	border: 0;
	font-size: 13px;
	line-height: 26px;
}
caption {
	padding: 0 0 10px;
	font-weight: bold;
	text-align: left;
	caption-side: top;
}
td,
th {
	vertical-align: top;
	padding: 14px 20px;
	padding-left: 0;
	border: 1px solid $colorBd;
	border-width: 0 0 1px;
	font-family: $fontSecondary;
}
th {
	font-weight: bold;
	text-align: left;
}
thead th {
	background: $colorBg;
}
tbody tr:last-child th,
tbody tr:last-child td {
	border-bottom: 0;
}
tbody th {
	padding-left: 0;
	text-transform: uppercase;
}
tbody tr td:last-child {
	padding-right: 0;
}
// MQ
@media ($mdDown) {
	table {
		font-size: 12px;
	}
	td,
	th {
		padding-top: 22px;
		padding-bottom: 22px;
	}
	tbody td {
		position: relative;
	}
	tbody tr td:first-of-type {
		padding-left: 0;
	}

	tbody tr {
		td[data-title] {
			position: relative;
			padding-top: 66px;
			&::before {
				content: attr(data-title);
				position: absolute;
				top: 24px;
				color: $colorText;
				font-family: $fontSecondary;
				font-weight: 600;
				text-transform: uppercase;
			}
		}
		td[data-title] + * {
			padding-top: 66px;
		}
	}
}

// Image
figure {
	margin-bottom: $typoSpaceVertical;
}
figcaption {
	margin-top: 0.5em;
}

img {
	max-width: 100%;
	height: auto;
}
