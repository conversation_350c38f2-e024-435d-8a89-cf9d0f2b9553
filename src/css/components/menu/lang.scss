.m-lang {
	&__btn {
		position: relative;
		padding: 0 30px 0 4px;
		color: var(--color-link);
		line-height: 20px;
		transition: color $t;
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			right: 14px;
			margin-top: -2px;
			border-width: 4px 4px 0;
			border-style: solid dashed;
			border-right-color: transparent;
			border-left-color: transparent;
			transition: transfrom $t;
		}
	}
	&__list {
		@extend %reset-ul;
		position: absolute;
		right: 0;
		bottom: 0;
		left: 0;
		display: none;
		justify-content: space-around;
		align-items: center;
		height: 64px;
		padding: 14px 24px;
		background: $colorWhite;
		font-size: 10px;
		&::before {
			content: '';
			position: absolute;
			right: 45px;
			bottom: 100%;
			border-width: 0 8px 8px;
			border-style: solid dashed;
			border-color: $colorWhite transparent;
		}
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		display: block;
		text-decoration: none;
	}

	// STATEs
	.is-open &__btn {
		&::after {
			transform: rotate(-180deg);
		}
	}
	.is-open &__list {
		display: flex;
	}

	// HOVERS
	.is-open &__btn,
	.hoverevents &__btn:hover {
		color: $colorHover;
	}
	.hoverevents &__link:hover,
	&__link.is-active {
		color: $colorMineShaft;
	}
	.hoverevents .light-header &__btn:hover,
	.light-header .is-open &__btn {
		color: $colorWhite;
	}

	// MQ
	@media ($smUp) {
		&__list {
			height: 80px;
			font-size: 14px;
		}
	}
	@media ($xlUp) {
		position: relative;
		&__btn {
			z-index: 1;
			padding: 0 $spacing8 0 $spacing4;
			&::after {
				right: $spacing4;
			}
		}
		&__list {
			top: 100%;
			right: 0;
			bottom: auto;
			left: auto;
			display: block;
			height: auto;
			margin-top: $spacing6;
			padding: $spacing4 $spacing7 0;
			outline: 5000px solid rgba($colorDark, 0.7);
			font-size: 10px;
			visibility: hidden;
			opacity: 0;
			transition: opacity $t, visibility 0s $t;
			&::before {
				right: 20px;
			}
		}
		&__item {
			padding-bottom: $spacing4;
		}

		// STATES
		.is-open &__btn {
			color: $colorWhite;
		}
		.is-open &__list {
			display: block;
			visibility: visible;
			opacity: 1;
			transition-delay: 0s, 0s;
		}

		// HOVERS
		.hoverevents .is-open &__btn:hover {
			color: $colorWhite;
		}
	}
}
