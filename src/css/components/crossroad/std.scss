.c-std {
	&__top {
		margin-bottom: $spacing6;
	}
	&__title {
		line-height: (36/30);
	}
	&__annot {
		letter-spacing: normal;
	}
	&__list {
		display: grid;
		gap: $spacing6 $spacing2;
	}

	// MODIF
	&--w-bg {
		padding: $spacing10 0;
	}
	&--highlight &__list {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}
	&--highlight &__item:first-child {
		grid-column: span 2;
	}

	// MQ
	@media ($mdDown) {
		.content-indented {
			padding: 0;
		}
	}
	@media ($smUp) {
		&__annot {
			column-gap: $spacing6;
			column-count: 2;
			font-size: 14px;
			line-height: (24 / 14);
		}
		&__list {
			grid-template-columns: repeat(2, minmax(0, 1fr));
		}
	}
	@media ($mdUp) {
		&__top {
			margin-bottom: $spacing10;
		}
		&__list {
			gap: $spacing8 $spacing6;
		}
	}
	@media ($lgUp) {
		&__top {
			margin-bottom: $spacing13;
		}
		&__title {
			line-height: (60/40);
		}
		&__annot {
			column-gap: $spacing8;
			padding-top: $spacing10;
		}
		&__list,
		&--highlight &__list {
			grid-template-columns: repeat(3, minmax(0, 1fr));
			gap: $spacing9 $spacing8;
		}

		// MODIF
		&__list--4 {
			grid-template-columns: repeat(4, minmax(0, 1fr));
		}
		&--highlight &__item:first-child {
			grid-row: span 2;
			grid-column: span 1;
			.b-std {
				display: flex;
				flex-direction: column;
				height: 100%;
			}
		}
		&--w-bg {
			padding: $spacing19 0;
		}
	}
}
