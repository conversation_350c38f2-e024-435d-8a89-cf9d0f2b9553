.c-about {
	&__wrap {
		overflow: hidden;
	}
	&__item {
		position: relative;
		&::before {
			content: '';
			position: absolute;
			bottom: -52px;
			left: 50%;
			width: 40px;
			height: 1px;
			background: rgba($colorBd, 0.5);
			transform: translateX(-50%);
		}
	}

	// VARIANTs
	&__item:last-child {
		&::before {
			display: none;
		}
	}

	// MQ
	@media ($mdUp) {
		&__item {
			&::before {
				top: 50%;
				right: -52px;
				left: auto;
				width: 1px;
				height: 136px;
				transform: translateY(-50%) translateX(0);
			}
		}
	}

	@media ($lgUp) {
		&__item {
			&::before {
				right: -44px;
			}
		}
	}
}
