.b-references {
	font-size: 14px;
	line-height: (24 / 14);
	&__header {
		grid-area: header;
		max-width: 305px;
	}
	&__img {
		position: relative;
		grid-area: img;
		margin-bottom: $spacing8;
		padding: $spacing7 0;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			right: 76px;
			bottom: 0;
			left: -20px;
			width: 100%;
			height: 100%;
			border: 1px solid $colorBd;
		}
		.img::before {
			padding-top: percentage(844/830);
		}
	}
	&__content {
		grid-area: content;
	}
	&__annot {
		&::before {
			content: open-quote;
		}
		&::after {
			content: close-quote;
		}
	}
	&__author {
		strong {
			display: block;
		}
	}

	// MQ
	@media ($mdUp) {
		&__wrapper {
			display: grid;
			grid-template-columns: percentage(312/696) 1fr;
			grid-template-rows: auto auto;
			grid-template-areas:
				'img header'
				'img content';
			row-gap: 0;
			column-gap: $spacing10;
		}
		&__img {
			padding: 0;
			text-align: right;
			&::before {
				top: -$spacing9;
				right: $spacing18;
				bottom: $spacing9;
				left: #{-$rowMainGutter - 1px};
				width: auto;
				height: auto;
			}
			// .img::before {
			// 	padding-top: percentage(368/312);
			// }
		}
		&__header {
			align-self: end;
		}
	}
	@media ($xlUp) {
		&__wrapper {
			grid-template-columns: percentage(756/1288) 1fr;
			grid-template-rows: auto auto;
			grid-template-areas:
				'img header'
				'img content';
			row-gap: 0;
			column-gap: 132px;
		}
		&__img {
			&::before {
				top: -($spacing18);
				left: -($spacing19);
				width: 632px;
				height: 440px;
			}
			// .img::before {
			// 	padding-top: percentage(556/756);
			// }
		}
	}
}
