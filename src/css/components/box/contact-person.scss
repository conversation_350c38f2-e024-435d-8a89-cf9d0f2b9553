.b-contact-person {
	&__title {
		font-size: 24px;
		line-height: (34/24);
	}
	&__location {
		margin-bottom: $spacing1;
		& > * {
			margin-bottom: $spacing6;
		}
	}
	&__person {
		margin: 0 (-$rowMainGutterSm);
	}
	&__inner {
		position: relative;
		padding: $spacing9 0 0;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			bottom: #{$spacing11 + $spacing14};
			left: 50%;
			width: 100vw;
			margin-left: -50vw;
			background: var(--color-bg);
		}
	}
	textarea {
		height: 152px;
	}

	// MQ
	@media ($xlDown) {
		.content-indented-left {
			padding: 0;
		}
	}
	@media ($mdDown) {
		&__title br {
			display: none;
		}
	}
	@media ($mdUp) {
		&__title {
			font-size: 32px;
			line-height: (38/32);
		}
		&__location {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		&__select {
			max-width: 270px;
		}
		&__inner {
			padding: $spacing12 0 0;
		}
		&__person {
			margin: 0 auto;
		}
		textarea {
			height: 196px;
		}
	}
	@media ($lgUp) {
		&__title {
			font-size: 40px;
			line-height: (60/40);
		}
		&__location {
			margin-bottom: $spacing16;
			padding-bottom: $spacing4;
			border-bottom: 1px solid rgba($colorAbbey, 0.2);
		}
		&__inner {
			padding: $spacing14 0 $spacing15;
			&::before {
				right: 280px;
				bottom: 0;
				width: auto;
			}
		}
		&__grid {
			flex-wrap: nowrap;
		}
		@media ($xlDown) {
			&__title br {
				display: none;
			}
		}
	}
	@media ($xlUp) {
		&__grid {
			margin-left: 0;
		}
		&__cell {
			border-left-width: 0;
		}
	}
}
