.b-message {
	$s: &;
	.grid {
		margin-left: -$spacing10;
	}
	.grid__cell {
		border-left-width: $spacing10;
	}
	&__title {
		margin-bottom: $spacing4;
	}
	&__img {
		position: relative;
		margin: 0 (-$rowMainGutterSm);
		padding: $spacing4 0;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			right: $spacing9;
			bottom: 0;
			left: $spacing9;
			border: 1px solid $colorBd;
		}
		.img::before {
			padding-top: percentage(170/296);
		}
	}
	&__btn-wrap {
		margin-top: $spacing7;
	}

	// MQ
	@media ($lgDown) {
		&__btn-wrap .btn__text {
			min-width: 176px;
		}
	}
	@media ($mdDown) {
		&__title {
			padding: 0;
			&::before {
				content: none;
			}
		}
		&__content {
			max-width: 208px;
			margin: 0 auto;
			text-align: center;
		}
		&__btn-wrap {
			text-align: center;
			.btn::before {
				content: none;
			}
		}
	}
	@media ($mdUp) {
		&__content {
			padding-top: $spacing8;
		}
		&__title {
			margin-bottom: $spacing8;
		}
		&__img {
			margin: 0;
			padding: $spacing6 0 0 $spacing6;
			&::before {
				top: 0;
				right: $spacing6;
				bottom: $spacing6;
				left: 0;
			}
			.img::before {
				padding-top: percentage(228/248);
			}
		}
		&__btn-wrap {
			margin-top: $spacing9;
		}
	}
	@media ($lgUp) {
		&:not(&--lg) &__content {
			font-size: 14px;
			line-height: (20/14);
		}
		&--lg {
			.grid {
				margin-left: -$spacing16;
			}
			.grid__cell {
				border-left-width: $spacing16;
			}
			#{$s} {
				&__title {
					margin-bottom: $spacing10;
					font-size: 40px;
					line-height: (60/40);
				}
				&__img {
					padding: $spacing12 0 0 $spacing13;
					&::before {
						top: 0;
						right: $spacing13;
						bottom: $spacing12;
						left: 0;
					}
					.img::before {
						padding-top: percentage(388/492);
					}
				}
				&__content {
					padding-top: $spacing16;
				}
				&__btn-wrap {
					margin-top: $spacing13;
				}
			}
		}
	}
}
