.b-store {
	padding-top: $spacing6;
	font-size: 13px;
	line-height: (24 / 13);
	overflow: hidden;
	&__img-wrap {
		position: relative;
		margin: 0 (-$rowMainGutterSm) $spacing10;
		&::before {
			content: '';
			position: absolute;
			top: -$spacing6;
			bottom: -$spacing6;
			left: 88px;
			width: 2000px;
			background: var(--color-bg);
		}
	}
	&__img::before {
		padding-top: percentage(304/320);
	}
	&__hours.u-font-label {
		font-family: $fontPrimary;
	}

	// MQ
	@media ($lgDown) {
		.content-indented-left {
			padding: 0;
		}
	}
	@media ($mdUp) {
		padding-top: $spacing9;
		&__inner {
			display: flex;
			gap: $spacing10;
		}
		&__content {
			flex: 1 1 auto;
			align-self: center;
		}
		&__img-wrap {
			flex: 0 0 auto;
			width: percentage(312 / 695);
			margin: 0;
			&::before {
				top: $spacing9 * -1;
				right: 100px;
				bottom: $spacing8;
				left: auto;
				direction: rtl;
			}
		}
		&__img::before {
			padding-top: percentage(368/312);
		}
	}

	@media ($lgUp) {
		gap: $gridGutter;
		flex-direction: row-reverse;
		padding-top: $spacing18;
		font-size: 14px;
		line-height: (24 / 14);
		&__inner {
			flex-direction: row-reverse;
		}
		&__img-wrap {
			width: percentage(688 / 1288);
			&::before {
				top: $spacing18 * -1;
				bottom: 96px;
				left: 120px;
				direction: ltr;
			}
		}
		&__img::before {
			padding-top: percentage(480/688);
		}
	}
}
