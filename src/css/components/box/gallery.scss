.b-gallery {
	display: flex;
	flex-direction: column;
	margin-right: -$rowMainGutterSm;
	margin-left: -$rowMainGutterSm;
	&__sliders {
		flex: 1 1 auto;
		height: 90vh;
		.row-main {
			height: 100%;
		}
	}
	&__tabs {
		display: flex;
		justify-content: space-around;
		margin: 0;
		background: var(--color-bg);
	}
	&__link {
		display: block;
		color: var(--color-link);
		font-family: $fontSecondary;
		text-transform: uppercase;
		text-decoration: none;
	}
	&__main {
		position: relative;
		.embla__navigation {
			position: absolute;
			right: 0;
			bottom: 0;
			left: 0;
			padding: 10px 24px;
			background: rgba($colorWhite, 0.6);
		}
	}
	&__main,
	&__slider,
	.embla,
	.embla__viewport,
	.embla__container {
		height: 100%;
	}
	.video,
	.video__video,
	.video__video iframe {
		width: 100%;
		height: 100%;
	}
	&__btns {
		position: absolute;
		top: 50%;
		right: 8px;
		left: 8px;
		display: flex;
		justify-content: space-between;
		width: auto;
		margin: 0;
		transform: translateY(-50%);
		pointer-events: none;
		& > * {
			pointer-events: auto;
		}
		.embla__btn {
			color: $colorWhite;
			transition: background-color $t, opacity $t, visibility $t;
		}
		.icon-svg {
			width: 11px;
			height: 19px;
		}
	}
	&__tabs &__link {
		padding: 14px;
		font-size: 12px;
		letter-spacing: 1px;
	}
	&__main &__img {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 100%;
		margin: 0 auto;
		background: transparent;
		&::before {
			padding-top: percentage(612/1064);
		}
	}
	&__thumbs {
		padding: $spacing7 0;
	}
	&__thumblink {
		width: 116px;
		text-decoration: none;
		.play {
			width: 40px;
			height: 40px;
			border-width: 2px;
			&::after {
				margin: -7px 0 0 -4px;
				border-width: 7px 0 7px 10px;
			}
		}
	}
	&__thumbitem {
		display: flex;
	}
	&__thumblink &__img::after {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		border: 4px solid transparent;
		transition: border-color $t;
	}
	&__thumbname {
		position: absolute;
		top: -4px;
		right: -4px;
		left: -4px;
		display: block;
		padding: 26px 20px 10px;
		background: rgba(241, 241, 242, 0.8);
		color: $colorText;
		font-size: 14px;
		&::before {
			content: '';
			position: absolute;
			top: 100%;
			right: -4px;
			left: -4px;
			height: 100px;
			background: linear-gradient(to bottom, rgba($colorBg, 0.8), rgba($colorBg, 0.01));
		}
	}
	&__enlarge {
		position: absolute;
		right: 14px;
		bottom: -5px;
		padding: 10px;
		font-size: 0;
		.icon-svg:nth-child(2) {
			display: none;
		}
	}

	// STATES
	&:not(.has-navigation) &__thumbs--nav {
		display: none;
	}
	&__slider:not(.is-active),
	&__thumbs:not(.is-active) {
		display: none;
	}
	&__link.is-active {
		color: $colorMineShaft;
	}
	&__thumbitem.is-active &__img::after {
		border-color: var(--color-light);
	}
	&.has-navigation &__enlarge .icon-svg:nth-child(1) {
		display: none;
	}
	&.has-navigation &__enlarge .icon-svg:nth-child(2) {
		display: block;
	}
	&__btns .embla__btn[disabled] {
		visibility: hidden;
		opacity: 0;
	}
	.b-modal & {
		height: 100%;
	}
	.b-modal &__sliders {
		height: auto;
	}

	// HOVERS
	.hoverevents &__thumblink:hover &__img::after {
		border-color: $colorGrayLight;
	}
	.hoverevents &__thumblink.is-active:hover &__img::after {
		border-color: var(--color-light);
	}

	// MQ
	@media ($lgDown) {
		&__enlarge {
			color: $colorText;
		}
		&__btns {
			.embla__btn {
				width: 48px;
				height: 48px;
				border: 2px solid $colorWhite;
				border-radius: 50%;
				background: var(--color-btn);
			}
		}
		&__img .play {
			display: none;
		}

		.hoverevents &__btns .embla__btn:hover {
			background: var(--color-btn-hover);
			color: $colorWhite;
		}
	}

	@media ($mdUp) {
		margin-right: -$rowMainGutter;
		margin-left: -$rowMainGutter;
		&__btns {
			right: 24px;
			left: 24px;
		}
		&__tabs &__link {
			padding: 20px;
			font-size: 20px;
		}
		&__thumblink {
			width: 176px;
			.play {
				width: 48px;
				height: 48px;
			}
		}
	}
	@media ($lgUp) {
		margin-right: 0;
		margin-left: 0;
		.embla {
			margin: 0 60px;
			overflow: visible;
		}
		.embla__viewport {
			overflow: hidden;
		}
		&__sliders {
			margin-bottom: $spacing6;
		}
		&__tabs &__link {
			padding: 21px;
			font-size: 24px;
		}
		&__thumblink {
			width: 184px;
		}
		&__btns {
			.embla__btn {
				color: $colorWhite;
				color: $colorGrayDark;
			}
			.icon-svg {
				width: 32px;
				height: 56px;
			}
		}
		&__btns,
		&__main .embla__navigation {
			right: -60px;
			left: -60px;
		}
		&__main .embla__navigation {
			padding: 0;
			background: transparent;
		}
		&__enlarge {
			right: -14px;
			bottom: -14px;
		}
	}
}
