.b-intro-press {
	overflow: hidden;
	&:first-child {
		margin-top: -$hHeightSm;
	}
	&__top {
		padding-top: 81px;
		font-size: 16px;
		line-height: (26/16);
	}
	&__media {
		position: relative;
		margin: 0 -12px;
		.img {
			background: $colorWhite;
		}
	}
	&__contact {
		position: relative;
		z-index: 1;
		min-height: 220px;
		padding: $spacing10 $spacing6 $spacing11;
		color: $colorWhite;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			right: -$rowMainGutterSm;
			bottom: 0;
			left: -$rowMainGutterSm;
			z-index: -1;
			background: $colorDark;
		}
	}
	&__author-img {
		width: 68px;
		border-radius: 50%;
	}
	&__author-name {
		font-size: 13px;
		b {
			display: block;
		}
	}

	// MQ
	@media ($mdUp) {
		&:first-child {
			margin-top: -$hHeightMd;
		}
		&__top {
			padding-top: 100px;
		}
		&__media {
			margin: 0 (-$row<PERSON>ain<PERSON>utter);
			.img::before {
				padding-top: percentage(512/768);
			}
		}
		&__contact {
			padding: $spacing9 0 $spacing9 $spacing13;
			&::before {
				right: -5000px;
				left: 0;
			}
		}

		@media ($lgDown) {
			&__contact:first-child {
				margin-top: -110px;
			}
		}
	}
	@media ($lgUp) {
		&:first-child {
			margin-top: -$hHeightLg;
		}
		&__top {
			padding-top: 154px;
		}
		&__media {
			margin: 0;
		}
	}
	@media ($xlUp) {
		&__media {
			.img {
				width: 944px;
				&::before {
					padding-top: percentage(564/944);
				}
			}
		}
		&__contact {
			padding: $spacing9 0 $spacing9 $spacing13;
			&:first-child {
				margin-top: -110px;
			}
		}
		&__btns {
			min-height: 110px;
			padding-top: 35px;
		}
	}
}
