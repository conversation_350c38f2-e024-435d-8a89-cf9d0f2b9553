.b-intro-event {
	position: relative;
	padding: 81px 0 $spacing6;
	background: $colorDark;
	color: $colorWhite;
	&:first-child {
		margin-top: -$hHeightSm;
	}
	&__bg {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		&::after {
			content: '';
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			background: linear-gradient(
				to bottom,
				rgba($colorBlack, 99.9) 0%,
				rgba($colorBlack, 0.01) 30%,
				rgba($colorBlack, 0.01) 60%,
				rgba($colorBlack, 99.9) 100%
			);
		}
	}
	&__title {
		line-height: (48/40);
	}

	// MQ
	@media ($xlDown) {
		.content-indented {
			padding: 0;
		}
	}
	@media ($mdUp) {
		padding: 100px 0 $spacing9;
		&:first-child {
			margin-top: -$hHeightMd;
		}
	}
	@media ($lgUp) {
		padding: 155px 0 $spacing11;
		&:first-child {
			margin-top: -$hHeightLg;
		}
	}
}
