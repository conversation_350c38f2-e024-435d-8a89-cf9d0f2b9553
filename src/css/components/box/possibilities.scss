.b-possibilities {
	font-size: 13px;
	line-height: 24px;
	&__inner {
		position: relative;
		padding-top: $spacing8;
		padding-bottom: $spacing9;

		&::before {
			content: '';
			position: absolute;
			top: 0;
			right: -2000px;
			bottom: 0;
			left: -2000px;
			background: var(--color-bg);
		}
	}
	&__title {
		position: relative;
	}
	&__desc {
		position: relative;
		margin-bottom: 40px;
	}
	&__img::before {
		padding-top: percentage(360/296);
	}
	&__line {
		position: absolute;
		top: 20px;
		right: 20px;
	}
	&__range {
		position: absolute;
		bottom: 10%;
		left: 50%;
		width: calc(100% - 40px);
		max-width: 500px;
		transform: translateX(-50%);
	}

	//VARIANTs
	.no-js &__range {
		display: none;
	}
	// &__btn--light {
	// 	top: 25%;
	// }
	// &__btn--dark {
	// 	bottom: 25%;
	// }

	//MQ
	@media ($smUp) {
		&__img::before {
			padding-top: percentage(688/1200);
		}
	}
	@media ($mdUp) {
		&__inner {
			display: grid;
			grid-template-columns: 6fr 6fr;
			grid-template-areas:
				'title title'
				'column1 column2'
				'img img';
			gap: $spacing8 $spacing6;
			padding-top: $spacing12;
		}
		&__title {
			grid-area: title;
		}
		&__desc {
			margin-bottom: $spacing8;
		}
		&__img {
			grid-area: img;
		}

		//VARIANTs
		&__desc--1 {
			grid-area: column1;
		}
		&__desc--2 {
			grid-area: column2;
		}
	}

	@media ($lgUp) {
		font-size: 14px;
		&__inner {
			grid-template-columns: 6fr 5fr 5fr;
			grid-template-areas:
				'title column1 column2'
				'img img img';
			gap: $spacing12 $spacing8;
			padding-top: $spacing15;

			&::before {
				right: $rowMainGutter;
				bottom: 0;
				height: 880px;
			}
		}
		&__desc {
			margin: $spacing8 0 0;
		}
	}
}
