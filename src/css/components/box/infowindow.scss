.b-infowindow {
	$s: &;
	width: 300px;
	padding: 20px;
	background: $colorDark;
	color: $colorWhite;
	font-family: $fontPrimary;
	text-align: center;
	&__img {
		margin: -20px -20px $spacing9 !important;
	}
	&__title {
		color: $colorGoldLight;
		font-size: 24px;
		text-transform: uppercase;
	}
	&__link {
		color: inherit;
		text-decoration: none;
	}
	&__subtitle {
		font-family: $fontSecondary;
		text-transform: uppercase;
	}
	&__office-title {
		font-size: 20px;
	}
	&__direction {
		position: relative;
		z-index: 1;
		font-family: $fontSecondary;
		text-align: center;
	}
	// &__close {
	// 	position: absolute;
	// 	top: 0;
	// 	right: 0;
	// 	padding: 8px;
	// 	border: 0;
	// 	background: transparent;
	// 	color: $colorWhite;
	// 	// pointer-events: none;
	// 	.icon-svg {
	// 		width: 15px;
	// 	}
	// }

	&--event {
		padding: 0;
		text-align: left;
		#{$s}__inner {
			padding: 20px;
		}
		#{$s}__img {
			margin: 0;
			img {
				width: 100%;
				height: auto;
			}
		}
		#{$s}__title {
			color: $colorGrayLight;
		}
		#{$s}__address {
			font-family: $fontSecondary;
			text-transform: uppercase;
		}
		#{$s}__date {
			font-family: $fontSecondary;
			text-transform: uppercase;
		}
	}

	// STATES
	.hoverevents &__link:hover {
		color: $colorWhite;
	}

	// MQ
	@media ($mdDown) {
		&__img {
			display: none;
		}
	}
}

// close icon
.infoBox > img {
	position: absolute !important;
	top: 0;
	right: 0;
	z-index: 2;
	width: 31px;
	height: 31px;
	margin: 0;
	padding: 8px;
}
