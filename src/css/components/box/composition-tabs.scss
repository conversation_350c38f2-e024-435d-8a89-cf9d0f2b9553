.b-composition-tabs {
	$s: &;
	position: relative;
	&__grid.grid--scroll {
		gap: $gridGutter;
		margin: 0;
		padding-bottom: $gridGutter;
		overflow-x: scroll;
		.grid__cell {
			border: none;
		}
		/* width */
		&::-webkit-scrollbar {
			height: 3px;
		}
		/* Track */
		&::-webkit-scrollbar-track {
			background: $colorRaven;
		}
		/* Handle */
		&::-webkit-scrollbar-thumb {
			background: $colorBlack;
		}
		/* Handle on hover */
		&::-webkit-scrollbar-thumb:hover {
			background: $colorBlack;
		}
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		justify-content: center;
		&--space-between {
			justify-content: space-between;
		}
	}
	&__item {
		@extend %reset-ul-li;
		@extend %reset-ol-li;
	}
	&__navigation {
		padding: 0 32px;
		border-bottom: 1px solid $colorBd;
	}
	&__link {
		position: relative;
		display: block;
		padding: 0 0 32px;
		text-decoration: none;
		&::before {
			content: '';
			position: absolute;
			right: 0;
			bottom: -1px;
			left: 0;
			height: 1px;
			background-color: $colorMineShaft;
			visibility: hidden;
			opacity: 0;
			transition: opacity $t, visibility $t;
		}
	}
	&__link-img {
		display: block;
		width: 72px;
		margin-bottom: 24px;
		background-color: transparent;
	}
	&__link-text {
		display: block;
		color: $colorMineShaft;
		font-weight: 600;
		font-size: 13px;
		line-height: (18/13);
		text-align: center;
		text-transform: uppercase;
	}
	&__fragment {
		&::before {
			content: '';
			position: absolute;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: -1;
			height: 214px;
			background-color: var(--color-bg);
			opacity: 0.5;
		}
	}
	&__img {
		width: 248px;
		&::before {
			padding-top: percentage(304/448);
		}
	}

	// STATEs
	.embla.is-disabled .embla__container {
		/* stylelint-disable-next-line declaration-no-important */
		transform: none !important;
	}
	&__fragment:not(.is-active) {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		visibility: hidden;
		opacity: 0;
	}

	// HOVERs
	.hoverevents &__link:hover &__link-img {
		background: transparent;
	}
	.hoverevents &__item:not(.is-selected) &__link:hover {
		&::before {
			visibility: visible;
			opacity: 0.5;
		}
	}

	// MQ
	@media ($mdDown) {
		padding: 0 20px;
		&__navigation {
			position: relative;
			width: 100%;
			height: 48px;
			padding: 0;
		}
		&__dropdown {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: 1;
			z-index: 11;
			width: 100%;
			margin: 0;
			padding: 0;
			border: 0;
			background: transparent;
			font-size: 0;
			overflow: hidden;
		}
		&__list {
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			z-index: 10;
			flex-direction: column;
		}
		&__item {
			position: relative;
			display: block;
			order: 1;
			width: 100%;
			background-color: $colorWhite;
			&:not(.is-selected) {
				visibility: hidden;
				opacity: 0;
			}
		}
		&__link {
			display: flex;
			align-items: center;
			padding: 8px 4px;
		}
		&__link-img {
			width: 32px;
			margin: 0 20px 0 0;
		}

		// STATEs
		&__navigation.is-open {
			#{$s}__item:not(.is-selected) {
				visibility: visible;
				opacity: 1;
			}
			#{$s}__item.is-selected::before {
				transform: translateY(-50%) rotate(-180deg);
			}
		}
		&__item.is-selected {
			order: 0;
			&::before {
				content: '▾';
				position: absolute;
				top: 50%;
				right: 20px;
				color: $colorText;
				transform: translateY(-50%);
				transition: transform $t;
			}
		}
	}
	@media ($mdUp) {
		&__navigation {
			padding: 0;
		}
		&__dropdown {
			display: none;
		}
		&__list {
			margin-bottom: -1px;
		}
		&__item {
			flex: 0 0 auto;
		}
		&__link {
			padding: 0 25px 32px;
			border-bottom: 1px solid transparent;
			transition: border-color $t;
			&::before {
				display: none;
			}
		}
		&__img {
			width: 304px;
		}
		&__fragment {
			&::before {
				height: 246px;
			}
		}

		// STATEs
		&__item.is-selected &__link {
			border-bottom-color: $colorText;
		}
	}
	@media ($lgUp) {
		&__link-img {
			width: 88px;
		}
		&__img {
			width: 448px;
		}
		&__link {
			padding: 0 20px 24px;
		}
		&__fragment {
			&::before {
				height: 320px;
			}
		}
	}
}
