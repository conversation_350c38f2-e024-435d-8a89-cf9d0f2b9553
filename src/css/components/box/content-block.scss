.b-content-block {
	overflow: hidden;
	&__inner {
		& > * {
			margin-bottom: 32px;
		}
	}
	&__block {
		padding: 32px 56px;
		border: 1px solid $colorBd;
	}
	&__box {
		display: block;
		max-width: 136px;
		margin: 0;
	}
	&__highlight {
		display: block;
		margin: 0;
		line-height: 1;
	}

	// MQ
	@media ($xlDown) {
		.content-indented {
			padding: 0;
		}
	}
	@media ($smUp) {
		&__inner {
			& > * {
				margin-bottom: 30px;
			}
		}
		&__block {
			float: left;
			width: 50%;
			margin-right: 32px;
		}
	}
	@media ($mdUp) {
		&__inner {
			& > * {
				margin-bottom: 44px;
			}
		}
		&__block {
			width: 264px;
			margin-right: 72px;
			padding: $spacing12;
		}
		&__box {
			max-width: 225px;
			margin: 0 auto;
		}
		&__highlight {
			white-space: nowrap;
		}
	}
	@media ($lgUp) {
		&__block {
			width: 408px;
		}
	}
}
