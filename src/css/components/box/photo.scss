.b-photo {
	position: relative;
	&::before {
		content: '';
		position: absolute;
		top: 144px;
		right: $spacing5;
		bottom: 0;
		width: 2000px;
		background: var(--color-bg);
	}
	&__img {
		position: relative;
		margin: 0 (-$rowMainGutterSm) $spacing4;
		&::before {
			padding-top: percentage(224/320);
		}
	}
	&__desc {
		position: relative;
		margin: 0;
		padding: 0 #{$spacing5 + $spacing4} $spacing4 0;
		color: $colorRaven;
		font-size: 14px;
		line-height: (24/14);
	}

	// VARIANTs
	&--inverted::before {
		right: auto;
		left: $spacing6;
	}
	&--inverted &__desc {
		margin-left: auto;
		padding: 0 0 $spacing4 #{$spacing5 + $spacing4};
	}

	// MQ
	@media ($mdUp) {
		&::before {
			right: #{$rowMainGutter + $spacing9};
		}
		&__img {
			margin: 0 0 $spacing6;
			&::before {
				padding-top: percentage(400/616);
			}
		}
		&__desc {
			padding: 0 #{$row<PERSON>ain<PERSON>utter + $spacing9} $spacing6 0;
		}
		&--inverted::before {
			left: #{$rowMainGutter + $spacing9};
		}
		&--inverted &__desc {
			padding: 0 0 $spacing6 #{$rowMainGutter + $spacing9};
		}
	}
	@media ($lgUp) {
		&::before {
			right: calc(50% - 56px);
		}
		&__img {
			margin: 0 0 $spacing10;
			&::before {
				padding-top: percentage(484/928);
			}
		}
		&__desc {
			width: 50%;
			padding: 0 0 $spacing10;
		}
		&--inverted::before {
			left: calc(50% - 56px);
		}
		&--inverted &__desc {
			padding: 0 0 $spacing10;
		}
	}
}
