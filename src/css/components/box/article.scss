.b-article {
	display: flex;
	flex-direction: column;
	font-size: 14px;
	line-height: (24 / 14);
	.grid__cell > &,
	.grid-highlight__item > & {
		height: 100%;
	}
	&__img {
		&::before {
			padding-top: percentage(232/328);
		}
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			right: 0;
			bottom: 0;
			left: 0;
			background: linear-gradient(180deg, rgba($colorBlack, 0.0001) 0%, rgba($colorBlack, 0.5) 100%);
		}
	}
	&__info {
		position: absolute;
		right: 8px;
		bottom: 10px;
		left: 0;
		z-index: 1;
		display: flex;
		align-items: flex-end;
		margin: 0;
		color: $colorWhite;
		font-family: $fontSecondary;
		font-weight: 600;
		font-size: 10px;
		line-height: 16px;
		text-transform: uppercase;
	}
	&__categories {
		display: flex;
		flex-wrap: wrap;
	}
	&__category {
		padding-left: 8px;
		text-decoration: none;
	}
	&__time {
		margin-left: auto;
		padding-left: 8px;
		white-space: nowrap;
	}
	&__title {
		@include line-clamp(2);
		margin: $spacing4 $spacing2;
	}

	// MQ
	@media ($mdDown) {
		&__desc {
			margin: auto 8px 0;
		}
	}
	@media ($smUp) {
		.grid-highlight__item--highlighted &__img::before {
			padding-top: percentage(1/2);
		}
		&__title {
			@include line-clamp(2);
			height: 52px;
		}
	}
	@media ($mdUp) {
		&__info {
			right: $spacing4;
			bottom: $spacing4;
		}
		&__category,
		&__time {
			padding-left: $spacing4;
		}
		&__title {
			margin: $spacing4 0;
		}
	}
	@media ($lgUp) {
		&__title {
			height: 64px;
			margin: $spacing4 0 $spacing6;
		}
	}
}
