.f-newsletter {
	letter-spacing: normal;
	&__title {
		margin: 0 0 $spacing4;
		font-size: 24px;
		line-height: (34/24);
		letter-spacing: 1px;
	}
	&__wrap {
		margin: 0 0 $spacing2;
	}
	&__btn {
		width: 100%;
		margin-top: $spacing2;
	}

	// MQ
	@media ($lgDown) {
		&__note {
			font-size: 11px;
		}
	}

	@media ($smUp) {
		&__wrap {
			display: flex;
			align-items: flex-start;
		}
		&__inp-wrap {
			flex: 1 1 auto;
		}
		&__btn {
			width: 208px;
			margin: 0;
		}
	}
	@media ($mdUp) {
		&__title {
			font-size: 32px;
			line-height: (38/32);
		}
	}
	@media ($lgUp) {
		&__title {
			margin: 0 0 $spacing3;
			font-size: 20px;
			line-height: (32/20);
		}
	}
}
