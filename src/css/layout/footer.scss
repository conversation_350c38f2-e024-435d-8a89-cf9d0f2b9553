.footer {
	background: $colorDark;
	color: $colorWhite;
	font-size: 14px;
	line-height: (24 / 14);
	&__top {
		border-bottom: 1px solid $colorMineShaft;
	}
	&__middle-wrap {
		overflow: hidden;
	}
	&__middle {
		display: flex;
		flex-wrap: wrap;
		margin: 0 $gridGutterSm * -1 0 $spacing13 * -1;
	}
	&__group {
		flex: 0 0 auto;
		width: 100%;
		padding: 0 $gridGutterSm 0 $spacing13;
		border-left: 1px solid $colorMineShaft;
	}
	&__list {
		@extend %reset-ul;
		margin-bottom: $spacing13;
		font-family: $fontSecondary;
		font-weight: 600;
		font-size: 12px;
		line-height: 16px;
	}
	&__item {
		@extend %reset-ul-li;
		margin-top: 2px;
	}
	&__link {
		display: inline-block;
		vertical-align: top;
		padding: 13px 0;
		text-transform: uppercase;
		text-underline-offset: 15px;
		&::after {
			background: $colorWhite;
		}
	}
	&__logo {
		margin: 0 0 $spacing8;
		img {
			width: 110px;
			height: auto;
		}
	}
	&__copyrights {
		margin: 0 0 $spacing9;
	}
	&__social {
		display: flex;
		gap: $spacing5;
		flex-wrap: wrap;
		margin: 0;
	}
	&__social-link {
		.icon-svg {
			width: 20px;
		}
	}
	&__created {
		color: $colorRaven;
	}

	// STATEs
	.hoverevents &__link:hover,
	.hoverevents & button:hover,
	.hoverevents & a:hover {
		color: $colorWhite;
	}

	// MQ
	@media ($mdDown) {
		&__title {
			position: relative;
			&::before {
				content: '';
				position: absolute;
				top: 50%;
				right: 0;
				border-width: 6px 4px 0;
				border-style: solid;
				border-color: $colorWhite transparent transparent transparent;
				transform: translateY(-50%);
			}
		}
		&__logo,
		&__copyrights {
			text-align: center;
		}
		&__social {
			justify-content: center;
		}
		&__cookies,
		&__created {
			text-align: center;
		}

		// MODIF
		&__group--contact,
		&__group--menu3 {
			margin-bottom: $spacing13;
		}
		&__group--menu3 &__list {
			margin: 0;
		}

		// STATES
		&__title.is-open::before {
			transform: scale(-1) translateY(50%);
		}
		&__title:not(.is-open) + &__list {
			display: none;
		}
	}
	@media ($lgDown) {
		&__title {
			font-size: 24px;
		}
	}

	@media ($mdUp) {
		&__group {
			width: 50%;
			margin-bottom: $spacing13;
		}
		&__logo {
			margin-bottom: $spacing7;
		}
		&__list {
			margin-bottom: 0;
		}

		// VARIANTs
		&__group--contact {
			width: 100%;
		}
		&__group--bottom {
			display: flex;
			flex-direction: column;
			justify-content: flex-end;
		}
	}
	@media ($lgUp) {
		&__group {
			width: 50%;
		}
		&__copyrights {
			margin: 0 auto 0 0;
		}
		&__logo,
		&__created,
		&__cookies {
			margin: 0;
		}

		// VARIANTs
		&__group--menu1 {
			order: 1;
		}
		&__group--menu2 {
			order: 2;
		}
		&__group--menu3 {
			order: 3;
		}
		&__group--contact {
			order: 4;
		}
		&__group--bottom {
			gap: $spacing2 $spacing7;
			flex: 0 0 auto;
			flex-direction: row;
			flex-wrap: wrap;
			justify-content: flex-start;
			align-items: center;
			order: 5;
			width: 100%;
			margin-top: $spacing20;
		}
	}
	@media ($xlUp) {
		&__group {
			width: percentage(360/1504);
			margin-bottom: 0;
		}
		&__list {
			margin-bottom: 0;
		}

		// MODIF
		&__group--contact {
			width: percentage(424/1504);
		}
		&__group--bottom {
			width: 100%;
		}
	}
}
