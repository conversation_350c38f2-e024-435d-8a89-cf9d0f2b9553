.colors {
	&__inner {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		margin: 0 0 (-$spacing4) (-$spacing4);
		& > * {
			margin: 0 0 $spacing4 $spacing4;
		}
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		flex-wrap: wrap;
		margin: 0 0 -6px -6px;
		font-size: 0;
	}
	&__item {
		@extend %reset-ul-li;
		margin: 0 0 6px 6px;
	}
	&__link {
		display: block;
		padding: 4px;
		border: 2px solid transparent;
		border-radius: 50%;
		text-decoration: none;
		transition: border-color $t;
	}
	&__color {
		display: block;
		width: 16px;
		height: 16px;
		border-radius: 50%;
	}
	&__current {
		color: $colorMineShaft;
		font-weight: 600;
		font-size: 13px;
		text-transform: uppercase;
	}

	// STATES
	&__link.is-selected {
		border-color: $colorRaven;
	}

	// HOVERS
	.hoverevents &__link:not(.is-selected):hover {
		border-color: rgba($colorRaven, 0.5);
	}
}
