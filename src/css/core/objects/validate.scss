// .inp-validate {
// 	padding-right: 40px;
// }
.has-error .js-phone-input__wrapper {
	&::before {
		content: '';
		position: absolute;
		top: 0;
		bottom: 0;
		left: 115px;
		width: 1px;
		background: $colorRed;
	}
}
.inp-error-phone {
	display: none;
	.has-error & {
		display: block;
	}
}
// .inp-icon {
// 	position: absolute;
// 	top: 0;
// 	right: 0;
// 	width: 40px;
// 	height: 40px;
// 	background-position: right 10px center;
// 	background-repeat: no-repeat;
// 	background-size: 20px 20px;
// 	pointer-events: none;
// }
.has-error .js-phone-input__wrapper + .inp-icon,
.has-error .inp-validate + .inp-icon {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M9.98 19.96c2.905 0 5.363-1.087 7.372-3.26 1.738-1.927 2.62-4.168 2.648-6.72 0-2.933-1.086-5.39-3.259-7.373C14.813.869 12.56 0 9.98 0 7.074 0 4.63 1.073 2.648 3.218.883 5.146 0 7.4 0 9.98c.027 2.797 1.005 5.146 2.933 7.046C4.86 18.982 7.21 19.96 9.98 19.96zm3.706-5.866a.909.909 0 0 1-.326-.123l-3.38-3.38-3.381 3.38a.39.39 0 0 1-.285.123.909.909 0 0 1-.326-.123c-.163-.217-.163-.42 0-.61l3.38-3.381-3.34-3.381c-.19-.217-.19-.421 0-.611.19-.136.394-.136.612 0l3.34 3.38 3.421-3.38c.19-.136.394-.136.611 0 .163.217.163.42 0 .61L10.591 9.98l3.38 3.38c.163.218.163.421 0 .611a.39.39 0 0 1-.285.123z' fill='%23C70000'/%3e%3c/svg%3e");
}

.has-ok .js-phone-input__wrapper + .inp-icon,
.has-ok .inp-validate + .inp-icon {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M10 0c2.604 0 4.87.898 6.797 2.695C18.932 4.675 20 7.11 20 10c0 2.604-.898 4.87-2.695 6.797C15.325 18.932 12.89 20 10 20c-2.604 0-4.87-.898-6.797-2.695C1.068 15.325 0 12.89 0 10c0-2.604.898-4.87 2.695-6.797C4.675 1.068 7.11 0 10 0zm4.453 7.383c.182-.209.182-.404 0-.586-.182-.13-.377-.13-.586 0L7.93 12.344l-1.797-1.797c-.182-.156-.378-.156-.586 0-.156.182-.156.377 0 .586l2.07 2.07a.871.871 0 0 0 .313.117.374.374 0 0 0 .273-.117l6.25-5.82z' fill='%23417505' fill-rule='evenodd'/%3e%3c/svg%3e");
}
.has-ok textarea.inp-validate + .inp-icon,
.has-error textarea.inp-validate + .inp-icon {
	background-position: right 10px top 10px;
}
.has-error .js-phone-input__flag,
.has-ok .js-phone-input__flag {
	color: $colorText;
}
