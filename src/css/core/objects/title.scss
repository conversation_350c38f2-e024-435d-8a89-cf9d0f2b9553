%title {
	position: relative;
	margin-top: 0;
	padding-top: $spacing4;
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 40px;
		height: 1px;
		background: $colorBd;
	}
}
.title {
	@extend %title;

	// VARIANTs
	&--center {
		text-align: center;
		&::before {
			left: 50%;
			margin-left: -20px;
		}
	}
	&--count {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	// MQ
	@media ($mdUp) {
		padding-top: $spacing6;
	}
	@media ($lgUp) {
		padding-top: $spacing8;
	}
}
