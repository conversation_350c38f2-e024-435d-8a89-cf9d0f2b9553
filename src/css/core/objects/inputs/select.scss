.inp-select {
	@extend %inp;
	flex: 1;
	padding: 9px 35px 9px 15px;
	background-image: url($svgSelect);
	background-position: top 50% right 20px;
	background-repeat: no-repeat;
	background-size: 7px 4px;
	color: $colorRaven;
	text-transform: uppercase;
	transition: color $t, border-color $t;
	cursor: pointer;
	-webkit-tap-highlight-color: transparent;
	&::-ms-expand {
		display: none;
	}

	// MODIF
	&--underline {
		padding: 3px 3px 15px;
		border-width: 0 0 1px;
		border-color: $colorRaven;
		background-color: transparent;
		background-position: top 11px right 3px;
	}

	// HOVERS
	.hoverevents &:hover {
		border-color: $colorText;
		color: $colorText;
	}
}
