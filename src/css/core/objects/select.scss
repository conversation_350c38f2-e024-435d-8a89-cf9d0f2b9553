.select {
	position: relative;
	&__btn {
		position: relative;
		padding-right: 20px;
		background-image: none;
		text-align: left;
		&::before {
			content: '▾';
			position: absolute;
			top: 50%;
			right: 20px;
			color: $colorText;
			transform: translateY(-50%);
		}
	}
	&__box {
		position: absolute;
		top: 100%;
		// right: -56px;
		left: 0;
		z-index: 5;
		min-width: 100%;
		max-width: calc(100vw - 2 * #{$rowMainGutterSm});
		max-height: 200px;
		margin-top: -1px;
		padding: 32px 15px;
		background: $colorGrayLight;
		font-family: $fontSecondary;
		font-size: 12px;
		text-transform: uppercase;
		overflow-x: hidden;
		overflow-y: auto;
		visibility: hidden;
		opacity: 0;
		transition: none;
	}
	&__list {
		@extend %reset-ul;
		margin-bottom: -16px;
		line-height: 1;
	}
	&__item {
		@extend %reset-ul-li;
		display: -webkit-box;
		margin-bottom: 16px;
		white-space: nowrap;
	}
	&__link {
		display: block;
	}

	// MODIF
	&__btn.inp-select--underline::before {
		top: 0;
		right: 3px;
		transform: none;
	}

	// STATES
	&__btn:focus {
		border-color: inherit;
	}
	.is-open &__btn {
		border-color: transparent;
		background-color: $colorGrayLight;
		color: $colorText;
		&::before {
			transform: scale(-1) translateY(50%);
		}
		&.inp-select--underline {
			padding: 9px 35px 9px 15px;
			background-position: top 50% right 20px;
		}
		&.inp-select--underline::before {
			top: 50%;
			right: 20px;
			transform: scale(-1) translateY(50%);
		}
	}
	.is-open &__box {
		visibility: visible;
		opacity: 1;
	}
	&__link.is-active {
		color: $colorText;
	}

	// HOVERS
	.hoverevents .is-open &__btn:hover {
		border-color: transparent;
	}

	// MQ
	@media ($smUp) {
		&--reversed &__box {
			right: 0;
			left: auto;
		}
	}
	@media ($mdUp) {
		&__box {
			max-width: calc(100vw - 2 * #{$rowMainGutter});
		}
	}
}
