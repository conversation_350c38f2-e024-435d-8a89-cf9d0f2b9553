$track-color: $colorBg !default;
$thumb-color: $colorGoldMid !default;

$thumb-radius: 12px !default;
$thumb-height: 24px !default;
$thumb-width: 24px !default;
$thumb-shadow-size: 4px !default;
$thumb-shadow-blur: 4px !default;
$thumb-shadow-color: rgba(0, 0, 0, 0.2) !default;
$thumb-border-width: 2px !default;
$thumb-border-color: $colorWhite !default;

$track-width: 100% !default;
$track-height: 8px !default;
$track-shadow-size: 1px !default;
$track-shadow-blur: 1px !default;
$track-shadow-color: rgba(0, 0, 0, 0.2) !default;
$track-border-width: 2px !default;
$track-border-color: $colorBg !default;

$track-radius: 5px !default;
$contrast: 0% !default;

$ie-bottom-track-color: darken($track-color, $contrast) !default;

@mixin shadow($shadow-size, $shadow-blur, $shadow-color) {
	box-shadow: $shadow-size $shadow-size $shadow-blur $shadow-color, 0 0 $shadow-size lighten($shadow-color, 5%);
}

@mixin track {
	width: $track-width;
	height: $track-height;
	transition: all $t ease;
	cursor: default;
}

@mixin thumb {
	@include shadow($thumb-shadow-size, $thumb-shadow-blur, $thumb-shadow-color);
	width: $thumb-width;
	height: $thumb-height;
	border: $thumb-border-width solid $thumb-border-color;
	border-radius: $thumb-radius;
	background: $thumb-color;
	cursor: pointer;
	box-sizing: border-box;
}

.range {
	width: $track-width;
	margin: $thumb-height / 2 0;
	background: transparent;
	-webkit-appearance: none;

	&::-moz-focus-outer {
		border: 0;
	}

	&:focus {
		outline: 0;

		&::-webkit-slider-runnable-track {
			background: lighten($track-color, $contrast);
		}

		&::-ms-fill-lower {
			background: $track-color;
		}

		&::-ms-fill-upper {
			background: lighten($track-color, $contrast);
		}
	}

	&::-webkit-slider-runnable-track {
		@include track;
		@include shadow($track-shadow-size, $track-shadow-blur, $track-shadow-color);
		border: $track-border-width solid $track-border-color;
		border-radius: $track-radius;
		background: $track-color;
	}

	&::-webkit-slider-thumb {
		@include thumb;
		margin-top: ((-$track-border-width * 2 + $track-height) / 2 - $thumb-height / 2);
		-webkit-appearance: none;
	}

	&::-moz-range-track {
		@include shadow($track-shadow-size, $track-shadow-blur, $track-shadow-color);
		@include track;
		height: $track-height / 2;
		border: $track-border-width solid $track-border-color;
		border-radius: $track-radius;
		background: $track-color;
	}

	&::-moz-range-thumb {
		@include thumb;
	}

	&::-ms-track {
		@include track;
		border-width: ($thumb-height / 2) 0;
		border-color: transparent;
		background: transparent;
		color: transparent;
	}

	&::-ms-fill-lower {
		@include shadow($track-shadow-size, $track-shadow-blur, $track-shadow-color);
		border: $track-border-width solid $track-border-color;
		border-radius: ($track-radius * 2);
		background: $ie-bottom-track-color;
	}

	&::-ms-fill-upper {
		@include shadow($track-shadow-size, $track-shadow-blur, $track-shadow-color);
		border: $track-border-width solid $track-border-color;
		border-radius: ($track-radius * 2);
		background: $track-color;
	}

	&::-ms-thumb {
		@include thumb;
		margin-top: $track-height / 4;
	}

	&:disabled {
		&::-webkit-slider-thumb,
		&::-moz-range-thumb,
		&::-ms-thumb,
		&::-webkit-slider-runnable-track,
		&::-ms-fill-lower,
		&::-ms-fill-upper {
			cursor: not-allowed;
		}
	}
}
