import { Controller } from 'stimulus';
import { on, off } from '@superkoders/sk-tools/src/emmiter';

export const SnackbarTypes = {
	INFO: 'info',
	POSITIVE: 'positive',
	WARNING: 'warning',
	NEGATIVE: 'negative',
};

export default class Snackbar extends Controller {
	messageListener = null;

	addMessage(_type, props) {
		const message = document.createElement('div');
		message.className = `snackbar__item snackbar__item--${props.type || SnackbarTypes.INFO}`;
		message.innerHTML = props.message;

		this.element.appendChild(message);
		setTimeout(() => {
			this.element.removeChild(message);
		}, 3000);
	}

	connect() {
		this.messageListener = this.addMessage.bind(this);
		on('notify', this.messageListener);
	}

	disconnect() {
		if (this.messageListener) {
			off('notify', this.messageListener);
		}
	}
}
