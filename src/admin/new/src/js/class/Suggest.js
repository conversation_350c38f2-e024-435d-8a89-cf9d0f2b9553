import { create } from '@superkoders/sk-tools/src/emmiter';
import { on, off } from '@superkoders/sk-tools/src/event';
import { removeClass, addClass } from '@superkoders/sk-tools/src/className';

export class Suggest {
	constructor(node, options) {
		this.input = node;
		this.options = {
			minLength: 2,
			typeInterval: 500, // ms
			url: '/',
			loadingClass: 'is-loading',
			typingClass: 'is-typing',
			inputName: this.input.getAttribute('name'),
			...options,
		};

		this.isResult = false;
		this.typeTimer = null;
		this.searchTerm = '';
		this.ajax = [];

		const emmitter = create({});
		this.on = emmitter.on;
		this.off = emmitter.off;
		this.trigger = emmitter.trigger;

		this.keydown = this.keydown.bind(this);
		this.keypress = this.keypress.bind(this);
		this.focus = this.focus.bind(this);
		this.blur = this.blur.bind(this);
	}

	// prototype methods
	init() {
		this.input.setAttribute('autocomplete', 'off');

		on(this.input, 'keydown', this.keydown);
		on(this.input, 'keypress', this.keypress);
		on(this.input, 'focus', this.focus);
		on(this.input, 'blur', this.blur);

		return this;
	}

	destroy() {
		off(this.input, 'keydown', this.keydown);
		off(this.input, 'keypress', this.keypress);
		off(this.input, 'focus', this.focus);
		off(this.input, 'blur', this.blur);

		return this;
	}

	focus() {
		this.trigger('suggeststart');

		this.type();
	}

	blur() {
		this.trigger('suggestend');

		removeClass(this.input, this.options.typingClass);
	}

	keydown(e) {
		this.typeTimer = clearTimeout(this.typeTimer);
		this.typeTimer = setTimeout(() => this.type(), this.options.typeInterval);

		let isDefaultPrevented = false;

		this.trigger('typestart', {
			which: e.which,
			preventDefault() {
				isDefaultPrevented = true;
			},
		});

		if (isDefaultPrevented) {
			e.preventDefault();
		} else {
			addClass(this.input, this.options.typingClass);
		}
	}

	keypress() {
		this.trigger('typing');

		if (this.input.value !== this.searchTerm) {
			this.ajaxAbort();
		}
	}

	ajaxRemove(instance) {
		this.ajax = this.ajax.filter((item) => item !== instance);
	}

	ajaxAbort() {
		this.ajax.forEach((item) => item.abort());
	}

	type() {
		const { value } = this.input;
		const o = this.options;

		if (value !== this.searchTerm) {
			this.searchTerm = value;

			if (value.length >= o.minLength) {
				this.ajaxAbort();

				// Sestavení dat k poslání na server
				const data = new FormData();

				data.append(this.options.inputName, value);

				// Check if the suggest is part of the list and send already selected values
				const suggestWrapper = this.input.closest('[data-suggestinp-target="wrapper"]');
				const listParent = this.input.closest('[data-customfieldlist-target="content"],[data-list-target="list"]');

				if (listParent) {
					const suggestSiblings = Array.from(listParent.querySelectorAll('[data-suggestinp-target="wrapper"]'));

					const selectedSiblingsIds = suggestSiblings.reduce((ids, sibling) => {
						if (!suggestWrapper.isSameNode(sibling)) {
							const suggestInp = sibling.querySelector('[data-suggestinp-target="idInput"]');
							if (suggestInp && Boolean(suggestInp.value)) {
								ids.push(suggestInp.value);
							}
						}

						return ids;
					}, []);

					if (selectedSiblingsIds.length) {
						data.append('selectedSiblingsIds', selectedSiblingsIds.join('|'));
					}
				}

				let isDefaultPrevented = false;
				this.trigger('beforeSend', {
					data,
					preventDefault() {
						isDefaultPrevented = true;
					},
				});

				if (isDefaultPrevented) {
					return false;
				}

				addClass(this.input, o.loadingClass);

				const controller = new AbortController();
				const { signal } = controller;
				const ajax = {
					state: 'pending',
					abort() {
						controller.abort();
					},
				};

				fetch(this.options.url, {
					signal,
					method: 'POST',
					body: data,
				})
					.then((response) => {
						return response.text();
					})
					.then((respond) => {
						ajax.state = 'done';
						this.ajaxRemove(ajax);
						this.trigger('success', { respond });

						removeClass(this.input, o.loadingClass);
						this.isResult = true;
					})
					.catch(() => {
						ajax.state = 'done';
						this.ajaxRemove(ajax);
						if (!this.ajax.some((item) => item.state === 'pending')) {
							removeClass(this.input, o.loadingClass);
						}
					});

				this.ajax.push(ajax);
			} else if (this.isResult) {
				this.isResult = false;

				this.ajaxAbort();

				this.trigger('typeclear');
			} else {
				this.trigger('typeend');
			}
		} else {
			this.trigger('typeend');
		}

		removeClass(this.input, o.typingClass);
	}

	clear() {
		this.input.value = '';
		this.typeTimer = clearTimeout(this.typeTimer);
		this.type();
	}
}
