import { Controller } from 'stimulus';
import loadGmapsApi from '../tools/loadGmapsApi';

// see https://bitbucket.org/superkoders/snippets/src/master/src/js/components/gmap.js for more customization
export default class Gmap extends Controller {
	static values = {
		mapcenter: Object,
		markers: Array,
		zoom: Number,
		blank: <PERSON>olean,
	};
	static api = null;

	map = null;
	bounds = null;
	clusterer = null;
	infoBox = null;
	markers = [];
	visibleMarkers = [];

	connect() {
		const { options } = window.App;

		if (!options) {
			console.warn('Gmap: V App.run() nejsou definovane options!');
			return;
		}

		if (!this.hasMarkersValue && !this.hasMapcenterValue) {
			console.warn('Gmap: Either markers or map center has to be defined!');
			return;
		}

		if (Gmap.api) {
			this.loadMap();
		} else {
			loadGmapsApi().then((api) => {
				Gmap.api = api;
				this.loadMap();
			});
		}
	}

	loadMap() {
		this.element.classList.remove('is-loading');
		const { options } = window.App;
		const center = this.hasMapcenterValue ? this.mapcenterValue : this.markersValue[0].position;
		const icon = {
			url: `${options.assetsUrl}img/bg/marker.png`,
			anchor: new Gmap.api.Point(30, 30),
			size: new Gmap.api.Size(60, 60),
			scaledSize: new Gmap.api.Size(60, 60),
		};
		const iconBig = {
			url: `${options.assetsUrl}img/bg/marker-active.png`,
			anchor: new Gmap.api.Point(40, 40),
			size: new Gmap.api.Size(80, 80),
			scaledSize: new Gmap.api.Size(80, 80),
		};
		const clusterStyles = [
			{
				url: `${options.assetsUrl}img/bg/cluster.png`,
				height: 80,
				width: 80,
				anchor: [0, 0],
				textColor: '#fff',
				textSize: 20,
				fontFamily: '"Gilroy", sans-serif',
			},
		];
		const mapOptions = {
			center,
			zoom: this.zoomValue || 16,
			minZoom: 2,
			disableDefaultUI: true,
		};
		this.bounds = new Gmap.api.LatLngBounds();

		this.map = new Gmap.api.Map(this.element, mapOptions);

		this.infoBox = new window.InfoBox({
			closeBoxURL: `${options.assetsUrl}img/bg/close-white.svg`,

			alignBottom: true,
			// offset from bottom left corner
			pixelOffset: new Gmap.api.Size(-150, -60),
			enableEventPropagation: true,
		});

		this.infoBox.addListener('closeclick', () => {
			this.markers.forEach((marker) => marker.setIcon(icon));
		});

		this.markersValue.forEach((marker) => {
			const mapMarker = new Gmap.api.Marker({
				icon,
				map: this.map,
				position: marker.position,
				region: marker.region,
				markerType: marker.type,
			});

			let infoWindowContent = `
				<div class="b-infowindow link-mask">
					${marker.img ? `<p class="b-infowindow__img img img--3-2"><img src="${marker.img}" loading="lazy" alt="${marker.title}"></p>` : ''}
					${
						marker.title
							? `<h3 class="b-infowindow__title">
						${marker.link ? `<a href="${marker.link}" class="b-infowindow__link link-mask__link">${marker.title}</a>` : `${marker.title}`}
					</h3>`
							: ''
					}
					${marker.subtitle ? `<p class="b-infowindow__subtitle">${marker.subtitle}</p>` : ''}
					${marker.officeTitle ? `<p class="b-infowindow__office-title">${marker.officeTitle}</p>` : ''}
					${marker.info ? `<p class="b-infowindow__info">${marker.info}</p>` : ''}
					${marker.address ? `<p class="b-infowindow__address">${marker.address}</p>` : ''}
					${marker.tel ? `<p class="b-infowindow__tel">${marker.tel}</p>` : ''}
					${marker.telInfo ? `<p class="b-infowindow__tel-info">${marker.telInfo}</p>` : ''}
					${marker.email ? `<p class="b-infowindow__email">${marker.email}</p>` : ''}
					${
						marker.directionLink
							? `<p class="b-infowindow__direction"><a href="${marker.directionLink}" class="footer__link link"${
									marker.isExternal ? ' target="_blank"' : ''
							  }>${marker.directionText ? marker.directionText : marker.directionLink}</a></p>`
							: ''
					}

				</div>
			`;
			// <button type="button" class="b-infowindow__close">
			// 			<span class="icon-svg icon-svg--close" aria-hidden="true">
			// 				<svg class="icon-svg__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
			// 					<use xlink:href="/static/img/bg/icons-svg.svg#icon-close" width="100%" height="100%" focusable="false"></use>
			// 				</svg>
			// 			</span>
			// 		</button>

			if (marker.type === 'event') {
				infoWindowContent = `
					<div class="b-infowindow b-infowindow--event">
						${marker.link ? `<a href="${marker.link}">` : ''}
						${marker.image ? `<p class="b-infowindow__img"><img src="${marker.image}" alt="${marker.title}" /></p>` : ''}
						<div class="b-infowindow__inner">
							<h3 class="b-infowindow__title">${marker.title}</h3>
							${marker.date ? `<p class="b-infowindow__date">${marker.date}</p>` : ''}
							${marker.address ? `<p class="b-infowindow__address">${marker.address}</p>` : ''}

						</div>
						${marker.link ? '</a>' : ''}
					</div>
				`;
				// <button type="button" class="b-infowindow__close">
				// 				<span class="icon-svg icon-svg--close" aria-hidden="true">
				// 					<svg class="icon-svg__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
				// 						<use xlink:href="/static/img/bg/icons-svg.svg#icon-close" width="100%" height="100%" focusable="false"></use>
				// 					</svg>
				// 				</span>
				// 			</button>
			}

			mapMarker.addListener('click', () => {
				this.infoBox.setContent(infoWindowContent);
				this.infoBox.open(this.map, mapMarker);
				this.markers.forEach((marker) => marker.setIcon(icon));
				mapMarker.setIcon(iconBig);

				this.map.setCenter(this.infoBox.getPosition());
				this.map.panBy(0, -160);
			});
			this.markers.push(mapMarker);
			this.bounds.extend(marker.position);
		});

		this.clusterer = new window.MarkerClusterer(this.map, this.markers, { styles: clusterStyles });

		if (this.markers.length > 1 && this.bounds) {
			this.map.fitBounds(this.bounds);
		}

		this.visibleMarkers = [...this.markers];
		this.map.setOptions({ styles: this.blankValue === true ? mapStyles.blank : mapStyles.standard });
	}

	refreshMarkers() {
		// clear bounds, clusterer, hide markers
		this.bounds = new Gmap.api.LatLngBounds();
		this.clusterer.clearMarkers();

		// show filtered markers and change bounds and clusters
		this.visibleMarkers.forEach((marker) => {
			marker.setMap(this.map);
			this.bounds.extend(marker.position);
		});

		if (this.visibleMarkers.length > 1) {
			this.map.fitBounds(this.bounds);
		}
		this.clusterer.addMarkers(this.visibleMarkers);
	}

	filterMarkers(types) {
		this.visibleMarkers.forEach((marker) => marker.setMap(null));
		this.visibleMarkers = this.markers.filter((marker) => types.includes(marker.markerType));
		this.refreshMarkers();
	}

	zoomTo(event) {
		const { region } = event.detail;

		if (region) {
			this.bounds = new Gmap.api.LatLngBounds();

			const zoomMarkers = this.visibleMarkers.filter((marker) => marker.region === region);
			zoomMarkers.forEach((marker) => {
				this.bounds.extend(marker.position);
			});

			if (zoomMarkers.length > 0) {
				this.map.fitBounds(this.bounds);

				var zoom = this.map.getZoom();
				this.map.setZoom(zoom > 9 ? 9 : zoom);
			}
		}
	}

	filterByType(event) {
		const { types } = event.detail;

		this.filterMarkers(types);
	}
}

const mapStyles = {
	standard: [
		{
			elementType: 'geometry',
			stylers: [
				{
					color: '#f5f5f5',
				},
			],
		},
		{
			elementType: 'labels.icon',
			stylers: [
				{
					visibility: 'off',
				},
			],
		},
		{
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#616161',
				},
			],
		},
		{
			elementType: 'labels.text.stroke',
			stylers: [
				{
					color: '#f5f5f5',
				},
			],
		},
		{
			featureType: 'administrative.land_parcel',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#bdbdbd',
				},
			],
		},
		{
			featureType: 'poi',
			elementType: 'geometry',
			stylers: [
				{
					color: '#eeeeee',
				},
			],
		},
		{
			featureType: 'poi',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#757575',
				},
			],
		},
		{
			featureType: 'poi.park',
			elementType: 'geometry',
			stylers: [
				{
					color: '#e5e5e5',
				},
			],
		},
		{
			featureType: 'poi.park',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#9e9e9e',
				},
			],
		},
		{
			featureType: 'road',
			elementType: 'geometry',
			stylers: [
				{
					color: '#ffffff',
				},
			],
		},
		{
			featureType: 'road.arterial',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#757575',
				},
			],
		},
		{
			featureType: 'road.highway',
			elementType: 'geometry',
			stylers: [
				{
					color: '#dadada',
				},
			],
		},
		{
			featureType: 'road.highway',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#616161',
				},
			],
		},
		{
			featureType: 'road.local',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#9e9e9e',
				},
			],
		},
		{
			featureType: 'transit.line',
			elementType: 'geometry',
			stylers: [
				{
					color: '#e5e5e5',
				},
			],
		},
		{
			featureType: 'transit.station',
			elementType: 'geometry',
			stylers: [
				{
					color: '#eeeeee',
				},
			],
		},
		{
			featureType: 'water',
			elementType: 'geometry',
			stylers: [
				{
					color: '#c9c9c9',
				},
			],
		},
		{
			featureType: 'water',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#9e9e9e',
				},
			],
		},
	],
	blank: [
		{
			elementType: 'geometry',
			stylers: [
				{
					color: '#f5f5f5',
				},
			],
		},
		{
			elementType: 'geometry.fill',
			stylers: [
				{
					color: '#dedede',
				},
			],
		},
		{
			elementType: 'labels',
			stylers: [
				{
					visibility: 'off',
				},
			],
		},
		{
			elementType: 'labels.icon',
			stylers: [
				{
					visibility: 'off',
				},
			],
		},
		{
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#616161',
				},
			],
		},
		{
			elementType: 'labels.text.stroke',
			stylers: [
				{
					color: '#f5f5f5',
				},
			],
		},
		{
			featureType: 'administrative',
			elementType: 'geometry',
			stylers: [
				{
					visibility: 'off',
				},
			],
		},
		{
			featureType: 'administrative.land_parcel',
			stylers: [
				{
					visibility: 'off',
				},
			],
		},
		{
			featureType: 'administrative.land_parcel',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#bdbdbd',
				},
			],
		},
		{
			featureType: 'administrative.neighborhood',
			stylers: [
				{
					visibility: 'off',
				},
			],
		},
		{
			featureType: 'poi',
			stylers: [
				{
					visibility: 'off',
				},
			],
		},
		{
			featureType: 'poi',
			elementType: 'geometry',
			stylers: [
				{
					color: '#eeeeee',
				},
			],
		},
		{
			featureType: 'poi',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#757575',
				},
			],
		},
		{
			featureType: 'poi.park',
			elementType: 'geometry',
			stylers: [
				{
					color: '#e5e5e5',
				},
			],
		},
		{
			featureType: 'poi.park',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#9e9e9e',
				},
			],
		},
		{
			featureType: 'road',
			stylers: [
				{
					visibility: 'off',
				},
			],
		},
		{
			featureType: 'road',
			elementType: 'geometry',
			stylers: [
				{
					color: '#ffffff',
				},
			],
		},
		{
			featureType: 'road',
			elementType: 'labels.icon',
			stylers: [
				{
					visibility: 'off',
				},
			],
		},
		{
			featureType: 'road.arterial',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#757575',
				},
			],
		},
		{
			featureType: 'road.highway',
			elementType: 'geometry',
			stylers: [
				{
					color: '#dadada',
				},
			],
		},
		{
			featureType: 'road.highway',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#616161',
				},
			],
		},
		{
			featureType: 'road.local',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#9e9e9e',
				},
			],
		},
		{
			featureType: 'transit',
			stylers: [
				{
					visibility: 'off',
				},
			],
		},
		{
			featureType: 'transit.line',
			elementType: 'geometry',
			stylers: [
				{
					color: '#e5e5e5',
				},
			],
		},
		{
			featureType: 'transit.station',
			elementType: 'geometry',
			stylers: [
				{
					color: '#eeeeee',
				},
			],
		},
		{
			featureType: 'water',
			stylers: [
				{
					color: '#ffffff',
				},
			],
		},
		{
			featureType: 'water',
			elementType: 'geometry',
			stylers: [
				{
					color: '#c9c9c9',
				},
			],
		},
		{
			featureType: 'water',
			elementType: 'geometry.fill',
			stylers: [
				{
					color: '#ffffff',
				},
			],
		},
		{
			featureType: 'water',
			elementType: 'labels.text.fill',
			stylers: [
				{
					color: '#9e9e9e',
				},
			],
		},
	],
};
