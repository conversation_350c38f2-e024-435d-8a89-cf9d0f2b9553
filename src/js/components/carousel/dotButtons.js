export const setupDotBtns = (dotsArray, embla) => {
	dotsArray.forEach((dotNode, i) => {
		dotNode.addEventListener('click', () => embla.scrollTo(i), false);
	});
};

export const generateDotBtns = (dots, embla) => {
	const template = document.querySelector('.embla-dot-template').innerHTML;
	dots.innerHTML = embla.scrollSnapList().reduce((acc) => acc + template, '');
	var dotsArray = [].slice.call(dots.querySelectorAll('.embla__dot'));
	dotsArray.forEach((dot, i) => (dot.innerHTML = i + 1));
	return dotsArray;
};

export const selectDotBtn = (dotsArray, embla) => () => {
	const previous = embla.previousScrollSnap();
	const selected = embla.selectedScrollSnap();
	dotsArray[previous]?.classList.remove('is-selected');
	dotsArray[selected]?.classList.add('is-selected');
};

export const disableDots = (dots, embla) => {
	return () => {
		if (!embla) {
			dots.classList.add('is-disabled');
		} else {
			if (embla.canScrollPrev()) dots.classList.remove('is-disabled');
			else dots.classList.add('is-disabled');

			if (embla.canScrollNext()) dots.classList.remove('is-disabled');
			else dots.classList.add('is-disabled');
		}
	};
};
