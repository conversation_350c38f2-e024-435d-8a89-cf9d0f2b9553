{if isset($customContentItem->items)}
	{default $title = isset($customContentItem->title) ? $customContentItem->title : false }
	{default $subtitle = isset($customContentItem->subtitle) ? $customContentItem->subtitle : false }
	{default $content = isset($customContentItem->content) ? $customContentItem->content : false }
	{default $highlightFirst = isset($customContentItem->highlightFirst) ? $customContentItem->highlightFirst : false }
	{default $variant = isset($customContentItem->variantSmall) ? 'sm' : false }
	{default $spacing = isset($customContentItem->spacing) ? $customContentItem->spacing : false }

	{include $templates.'/part/box/features.latte', title=>$title, subtitle=>$subtitle, content=>$content, highlightFirst=>$highlightFirst, variant=>$variant, items=>$customContentItem->items, spacing=>$spacing}
{/if}
