
{if isset($customContentItem->items) || isset($customContentItem->newest)}
	{default $title = isset($customContentItem->title) ? $customContentItem->title : false }
	{default $subtitle = isset($customContentItem->subtitle) ? $customContentItem->subtitle : false }
	{default $items = isset($customContentItem->items) ? $customContentItem->items : false }
	{default $btn = isset($customContentItem->btn) ? $customContentItem->btn : false }
	{default $newest = isset($customContentItem->newest) ? $customContentItem->newest : false }
	{default $spacing = isset($customContentItem->spacing) ? $customContentItem->spacing : false }

	{if $newest}
		{include $templates.'/part/crossroad/references.latte', title=>$title, subtitle=>$subtitle, crossroad=>$references, spacing=>$spacing, pager=>false, button=>false, customBtn=>$btn, referenceTitle=>'h3'}
	{else}
		{include $templates.'/part/crossroad/references.latte', title=>$title, subtitle=>$subtitle, crossroad=>$items, spacing=>$spacing, pager=>false, button=>false, customBtn=>$btn, referenceTitle=>'h3'}
	{/if}
	{* {include $templates.'/part/crossroad/references.latte', crossroad=>$references, customTitle=>"title_references_last", button=>$pages->references, spacingBottom=>20, pager=>false, referenceTitle=>"h3"} *}
{/if}

