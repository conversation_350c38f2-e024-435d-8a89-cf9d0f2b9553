{default $class = 'u-mb-lg'}

<div class="c-compare {$class}">
	{if $compareProducts && $compareProducts->count() > 0}
	<div class="c-compare__header c-compare__group">
		<div class="c-compare__helper mh-compare">
			<div class="c-compare__param">
{*				{if $sameParam}*}
{*					*}{*stejné: {$cntSameParam}*}
{*					*}{*odlišné: {$cntDiffParam}*}
{*					{if $cntSameParam < 4}*}
{*						{_only} {$cntSameParam} {_}{$cntSameParam|plural: "same_parameter", "same_parameter_plural", "same_parameter_plural"}{/_}*}
{*					{elseif $cntDiffParam < 4}*}
{*						{_only} {$cntDiffParam} {_}{$cntDiffParam|plural: "diff_parameter", "diff_parameter_plural", "diff_parameter_plural"}{/_}*}
{*					{else}*}
{*						{$cntDiffParam} {_}{$cntDiffParam|plural: "odlišný parametr"," diff_parameter_plural", "diff_parameter_plural_2"}{/_}*}
{*					{/if}*}
{*				{/if}*}
			</div>
		</div>

		{foreach $allParamList as $paramI}
			<div class="c-compare__param mh-compare{if $sameParam && isset($sameParam[$paramI['id']])} c-compare__param--highlight{/if}"> {*class = c-compare__param--highlight kdyz je shoda*}
				{$paramI['name']}
			</div>
		{/foreach}

		<h2 class="h3 c-compare__param mh-compare c-compare__param--title">
			{_price}
		</h2>
		{if $compareProducts->count() > 1}
			<div class="c-compare__param mh-compare">
				{_price_save}
			</div>
		{/if}
		<div class="c-compare__param mh-compare">
			{_price} {_price_without_tax}
		</div>
		<div class="c-compare__param mh-compare">
			{_price} {_price_tax}
		</div>

	</div>
	<div class="c-compare__wrap">
		<div class="c-compare__list not-fixed">
			{foreach $compareProducts as $cp}
				<div class="c-compare__item u-bg--blue c-compare__group">
					<div class="c-compare__product">
						<div class="c-compare__tools">
							<a href="#" class="c-compare__tool c-compare__tool--move slick-move">
								{('move')|icon}
							</a>
							<a n:href="compareClick!, productId => $cp->id" class="c-compare__tool c-compare__tool--close{* slick-remove*}">
								{('close-thin')|icon}
							</a>
						</div>
						{include '../box/product.latte', product=>$cp, class=>'b-product--compare u-mb-sm'}
					</div>

					{foreach $allParamList as $param}
						{php $c = $cp->product->getParameterById($param['id'])}
						{if $c}
							<div class="c-compare__param mh-compare{if $sameParam && isset($sameParam[$param['id']])} c-compare__param--highlight{/if}" data-label="{$c->name}">
								{if $c->type == "text" || $c->type == "number"}
									{$c->valueObject->value} {$c->unit}
								{elseif $c->type == "bool"}
									{if $c->valueObject->value == 1}
										{_yes}
									{else}
										{_no}
									{/if}
								{else}
									{if isset($c->valueObjects)}
										{foreach $c->valueObjects as $v}
											{$v->value}{if !$iterator->last}, {/if}
										{/foreach}
									{else}
										{$c->valueObject->value}
									{/if}
								{/if}
							</div>
						{else}
							<div class="c-compare__param mh-compare" data-label="">
							</div>
						{/if}
					{/foreach}

					{*
					<h2 class="h3 c-compare__param mh-compare c-compare__param--title">
						{_price} {_price_without_tax}
					</h2>
					<h2 class="h3 c-compare__param mh-compare c-compare__param--title">
						{_price} {_price_tax}
					</h2>
					*}
					<h2 class="h3 c-compare__param mh-compare c-compare__param--title">
						{_price}
					</h2>
					{if $compareProducts->count() > 1}
						<div class="c-compare__param mh-compare" data-label="{_price_save}">
							{if $save[$cp->id] != 0}
								{$save[$cp->id]|priceFormat}
							{else}
								–
							{/if}
						</div>
					{/if}
					<div class="c-compare__param mh-compare" data-label="{_price} {_price_without_tax}">
						{if isset($cp->price)}
							{if $cp->price == 0}
								{_price_not_determined}
							{else}
								{$cp->price|priceFormat}
							{/if}
						{/if}
					</div>
					<div class="c-compare__param mh-compare" data-label="{_price} {_price_tax}">
						{if isset($cp->priceDPH)}
							{if $cp->priceDPH == 0}
								{_price_not_determined}
							{else}
								{$cp->priceDPH|priceFormat}
							{/if}
						{/if}
					</div>
				</div>
			{/foreach}
		</div>
	</div>

	<a href="#" class="c-compare__prev" aria-label="Předchozí" title="Předchozí">
		{('angle-left-polygon')|icon}
	</a>
	<a href="#" class="c-compare__next" aria-label="Předchozí" title="Následující">
		{('angle-right-polygon')|icon}
	</a>
	{else}
		{*zadny produkt v porovnavani*}
	{/if}
</div>
