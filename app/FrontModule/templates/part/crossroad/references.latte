{default $class = 'grid-highlight--auto'}
{default $spacingBottom = '16'}
{default $customTitle = false}
{default $title = false}
{default $subtitle = false}
{default $btnLang = 'btn_more_references'}

{default $pager = true}
{default $crossroad = false}
{default $ajaxPage = false}
{default $button = false}
{default $customBtn = false}

{php $isHp = $object->template == ':Front:Homepage:default'}
{if $isHp}
	{php $class = false}
{/if}

{if $crossroad && $crossroad->count() > 0}
	<section n:class="c-references, grid-highlight, $class, 'u-mb-' . $spacingBottom">
		<div n:if="$title || $customTitle" class="content-indented">
			<div class="u-mb-last-0 u-mb-6 u-mb-10@md u-mb-13@lg title">
				<p n:if="$subtitle" class="subtitle u-mb-2 u-mb-4@lg">
					{$subtitle}
				</p>
				<h2 n:if="$title || $customTitle" class="c-references__title h3 u-mt-0">
					{if $title}
						{$title}
					{else}
						{$customTitle|translate}
					{/if}
				</h2>
			</div>
		</div>

		<div class="c-references__list grid-highlight__list" n:snippet="referenceList" data-ajax-append>

			{default $class = 'grid-highlight--auto'}
			{default $title = false}
			{default $customTitle = false}
			{default $referenceTitle = $title || $customTitle ? 'h3' : 'h2'}
			{php $isHp = $object->template == ':Front:Homepage:default'}
			{if $isHp}
				{php $class = false}
			{/if}

			{foreach $crossroad as $item}
				<div class="c-references__item grid-highlight__item">
					{if ($iterator->counter % 18 == 14 || $iterator->counter % 18 == 4) && str_contains($class, 'grid-highlight--auto')}
						{var $isLarge = true}
					{else}
						{var $isLarge = false}
					{/if}

					{include '../box/reference.latte', class=>false, reference=>$item, referenceTitle=>$referenceTitle, isLarge=>$isLarge}
				</div>
			{/foreach}
		</div>

        {if $pager}
	        {snippet articlesPagerBottom}
                {control pager, [class => 'u-mt-14', najaScroll=>'.c-references__list']}
	        {/snippet}
        {/if}

		{if $button}
			<p class="btn-wrap u-mt-8 u-mt-9@md u-mt-14@lg">
				<a href="{plink $button}" class="btn btn--icon">
					<span class="btn__text">
						{$translator->translate($btnLang)}
						{("bevel-right")|icon}
					</span>
				</a>
			</p>
		{elseif $customBtn}
			<p class="btn-wrap u-mt-14">
				{if isset($customBtn->page)}
					<a href="{plink $customBtn->page}" class="btn btn--icon">
						<span class="btn__text">
							{if isset($customBtn->text)}
								{$customBtn->text}
							{else}
								{$customBtn->page->nameAnchor}
							{/if}
							{('bevel-right')|icon}
						</span>
					</a>
				{elseif isset($customBtn->external) && isset($customBtn->text)}
					<a href="{$customBtn->external}" class="btn btn--icon" target="_blank" rel="noopener noreferrer">
						<span class="btn__text">
							{$customBtn->text}
							{('bevel-right')|icon}
						</span>
					</a>
				{/if}
			</p>
		{/if}
	</section>
{elseif $pager}
	{include $templates.'/part/box/search-message.latte', title=>"msg_no_references"}
	{include $templates.'/part/crossroad/references.latte', crossroad=>$references, customTitle=>"title_references_last", button=>$pages->references, spacingBottom=>20, pager=>false}
	{include $templates.'/part/crossroad/articles.latte', crossroad=>$news, customTitle=>"title_articles_last", button=>$pages->blog, spacingBottom=>20, pager=>false}
{/if}
