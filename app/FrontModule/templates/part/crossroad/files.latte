{default $spacingBottom = isset($spacing->mobile) ? $spacing->mobile : '10'}
{default $spacingBottomMd = isset($spacing->tablet) ? $spacing->tablet : '14'}
{default $spacingBottomLg = isset($spacing->desktop) ? $spacing->desktop : '26'}
{default $title = false}
{default $subtitle = false}
{default $items = []}

<section id="downloads" n:if="count($items) > 0" class="c-files u-mb-{$spacingBottom} u-mb-{$spacingBottomMd}@md u-mb-{$spacingBottomLg}@lg">
	<div n:if="$subtitle || $title" class="content-indented">
		<div class="u-mb-6 u-mb-8@md u-mb-9@lg u-mb-last-0 title">
			<p n:if="$subtitle" class="subtitle u-mb-2 u-mb-4@lg">
				{$subtitle}
			</p>
			<h2 n:if="$title" class="h3 u-mt-0">
				{$title}
			</h2>
		</div>
	</div>
	{if count($items) > 2}
		<div class="c-files__list grid grid--x-6 grid--y-6 grid--x-8@lg grid--y-8@lg">
			<div n:foreach="$items as $item" class="c-files__item grid__cell size--6-12@sm size--4-12@lg size--3-12@xl">
				{include '../box/file.latte', item=>$item}
			</div>
		</div>
	{else}
		<div class="c-files__list grid grid--x-15@lg grid--y-12@lg" data-controller="Animate">
			<div n:foreach="$items as $item" class="c-files__item grid__cell size--6-12@md">
				{include '../box/file.latte', isLarge=>'true', item=>$item}
			</div>
		</div>
	{/if}
</section>
