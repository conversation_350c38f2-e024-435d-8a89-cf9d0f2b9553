{default $class = ''}
{default $productTitle = null}
{default $isLarge = false}
{var $variant = null}
{default $colors = false}
{default $isSmall = false}

{if $product}

	{if $product->isVariant}
		{php $link = $presenter->link($product, ['v'=>$product->id])}
		{var $variant = $product}
		{var $product = $product->product}
	{else}
		{* je to produkt - obalka*}
		{php $link = $presenter->link($product)}
		{var $activeVariant = $product->activeVariants->fetchAll()}
		{if count($activeVariant) == 1}
			{var $variant = $activeVariant[0]}
		{/if}
	{/if}

	<article n:class="b-product, $isLarge ? b-product--lg, link-extend, $class, $isSmall ? b-product--sm">
		<div class="b-product__img img img--contain">
			{if $product->firstImage}
				{if $isLarge}
					{php $img = $product->firstImage->getSize('product_lg')}
				{else}
					{php $img = $product->firstImage->getSize('product')}
				{/if}
				<img src="{$img->src}" alt="{$product->firstImage->getAlt($mutation)}" loading="lazy">
			{else}
				<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
			{/if}
		</div>
		<div class="b-product__content u-mb-last-0">
			<div class="b-product__top">
				<h3 n:tag="$productTitle" class="b-product__title h5 u-mb-0">
					<a href="{$link}" class="b-product__link link-extend__link">
						{$product->nameAnchor}
					</a>
				</h3>
				{if !$isSmall}
					{control "favourite-$product->id", isBtn=>true, obj=>$product, class=>'b-product__favorite'}
				{/if}
			</div>
			<p n:if="!$isSmall" class="u-font-label u-mb-6">
				{php $categoryColors = implode(' – ', array_filter([$product->mainCategory ? $product->mainCategory->nameAnchor : false, $colors]))}
				{$categoryColors}
				{capture $tr}{$colors|plural: "color", "colors", "colors_more" }{/capture}
				{$tr|translate}
			</p>
		</div>
	</article>
{/if}
