{default $item = false}
{default $showAnnot = false}
{default $class = false}
{default $titleTag = 'h2'}

{if $item}
	{php $cf = isset($item->cf) ? $item->cf : false}
	{if $item instanceof App\PostType\Reference\Model\Orm\ReferenceLocalization}
		{php $cfRef = $item->getParent() && isset($item->getParent()->cf) ? $item->getParent()->cf : false}
		{php $imgSrc = $cfRef && isset($cfRef->img_crossroad) && $cfRef->img_crossroad ? $cfRef->img_crossroad->getSize('submenu')->src : '/static/img/illust/noimg.svg'}
	{elseif $item instanceof App\PostType\Event\Model\Orm\EventLocalization || $item instanceof App\PostType\Person\Model\Orm\PersonLocalization || $item instanceof App\PostType\Designer\Model\Orm\DesignerLocalization}
		{php $cfRef = $item->getParent() && isset($item->getParent()->cf) ? $item->getParent()->cf : false}
		{php $imgSrc = $cfRef && isset($cfRef->img_crossroad) && $cfRef->img_crossroad ? $cfRef->img_crossroad->getSize('submenu')->src : '/static/img/illust/noimg.svg'}
	{elseif $item instanceof App\PostType\Page\Model\Orm\CommonTree && $cf && isset($cf->intro_media_article->poster) && $cf->intro_media_article->poster}
		{php $imgSrc = $cf && isset($cf->intro_media_article->poster) && $cf->intro_media_article->poster ? $cf->intro_media_article->poster->getSize('submenu')->src : '/static/img/illust/noimg.svg'}
	{elseif $item instanceof App\PostType\Page\Model\Orm\CommonTree && $cf && (isset($cf->social_images->fb) || isset($cf->social_images->tw))}
		{if isset($cf->social_images->fb)}
			{php $imgSrc = $cf->social_images->fb ? $cf->social_images->fb->getSize('submenu')->src : '/static/img/illust/noimg.svg'}
		{elseif isset($cf->social_images->tw)}
			{php $imgSrc = $cf->social_images->tw ? $cf->social_images->tw->getSize('submenu')->src : '/static/img/illust/noimg.svg'}
		{/if}
	{else}
		{php $imgSrc = $cf && isset($cf->img_crossroad) && $cf->img_crossroad ? $cf->img_crossroad->getSize('submenu')->src : '/static/img/illust/noimg.svg'}
	{/if}
	{php $imgAlt = $cf && isset($cf->img_crossroad) && $cf->img_crossroad ? $cf->img_crossroad->getAlt($mutation) : ''}
	{php $annot = $cf && isset($cf->annotation) ? $cf->annotation : false}

	<article n:class="b-page, link-mask, $class">
		<div class="b-page__img img">
			<img src="{$imgSrc}" alt="{$imgAlt}" loading="lazy">
		</div>
		<div class="b-page__bottom u-mb-last-0">
			<h2 n:tag="$titleTag" class="b-page__title">
				<a href="{plink $item}" class="b-page__link link-mask__link">
					{$item->nameAnchor}
				</a>
			</h2>
			<p n:if="$showAnnot && $annot" class="b-page__annot">
				{$annot}
			</p>
		</div>
	</article>
{/if}
