{default $spacingBottom = isset($spacing->mobile) ? $spacing->mobile : '10'}
{default $spacingBottomMd = isset($spacing->tablet) ? $spacing->tablet : '14'}
{default $spacingBottomLg = isset($spacing->desktop) ? $spacing->desktop : '26'}

{default $desc = isset($object->annotation) ? $object->annotation : ''}
{default $inverted = false}
{default $image = false}

<figure n:if="$image" class="b-photo full-vw{if $inverted} b-photo--inverted{/if} u-mb-{$spacingBottom} u-mb-{$spacingBottomMd}@md u-mb-{$spacingBottomLg}@lg" data-controller="Animate">
	<div class="row-main">
		<div class="b-photo__wrap u-clearfix u-mx-auto u-max-width-8-12">
			<div class="b-photo__img img" data-Animate-target="element" data-direction="{if $inverted}left{else}right{/if}">
				{php $img = $image->getSize('photo_center')}
				<img src="{$img->src}" alt="{$image->getAlt($mutation)}" loading="lazy">
			</div>
			<figcaption n:if="$desc" class="b-photo__desc">
				{$desc}
			</figcaption>
		</div>
	</div>
</figure>
