{default $spacingBottom = isset($spacing->mobile) ? $spacing->mobile : '10'}
{default $spacingBottomMd = isset($spacing->tablet) ? $spacing->tablet : '14'}
{default $spacingBottomLg = isset($spacing->desktop) ? $spacing->desktop : '26'}

{default $item = false}
{default $reverse = false}
{default $variant = 'v1'}
{default $imgSrc = isset($item->image) ? $item->image->getSize('section')->src : '/static/img/illust/noimg.svg'}
{default $imgAlt = isset($item->image) ? $item->image->getAlt($mutation) : ''}
{default $title = isset($item->title) ? $item->title : false}
{default $page = isset($item->btn) && isset($item->btn->page) ? $item->btn->page : false}
{default $external = isset($item->btn) && isset($item->btn->external) ? $item->btn->external : false}
{default $btnText = isset($item->btn->text) ? $item->btn->text : false}
{default $subtitle = isset($item->subtitle) ? $item->subtitle : false}
{default $content = isset($item->content) ? $item->content : false}

<article class="b-section full-vw b-section--{$variant}{if $reverse} b-section--reverse{/if} u-mb-{$spacingBottom} u-mb-{$spacingBottomMd}@md u-mb-{$spacingBottomLg}@lg" data-controller="Animate">
	<div class="row-main">
		<div class="b-section__inner">
			<div class="b-section__img-wrap">
				<div class="b-section__img img" data-animate-target="element"{if $reverse} data-direction="left"{else} data-direction="right"{/if}>
					<img src="{$imgSrc}" alt="{$imgAlt}" loading="lazy">
				</div>
			</div>
			<div class="b-section__content">
				<div class="u-max-width-4-12 u-mx-auto u-mb-last-0">
					<h3 n:if="$title" class="b-section__title {if $variant == 'v5'} h3{else} h4{/if} u-mb-0">
						{if $page}
							<a href="{plink $page}">
								{$title}
							</a>
						{elseif $external && $btnText}
							<a href="{$external}" target="_blank" rel="noopener noreferrer">
								{$title}
							</a>
						{else}
							{$title}
						{/if}
					</h3>
					<p n:if="$subtitle" class="u-font-label u-mt-6 u-mb-0">
						{$subtitle}
					</p>
					<p n:if="$content" class="u-mt-8 u-mb-0">
						{$content}
					</p>
					<ul n:if="isset($item->icons)" class="b-section__list u-mt-11">
						{foreach $item->icons as $icon}
							<li n:if="isset($icon->icon) && $icon->icon" class="b-section__item">
								<span class="b-section__icon u-mb-2">
									{php $imgSrc = $icon->icon->isSvg ? $icon->icon->url : $icon->icon->getSize('xs')->src}
									<img src="{$imgSrc}" alt="{$icon->icon->getAlt($mutation)}" width="64" height="64" loading="lazy">
								</span>
								{if isset($icon->text)}
									{$icon->text}
								{/if}
							</li>
						{/foreach}
					</ul>
					<p n:if="$page || ($external && btnText)" class="u-mt-10 u-no-print">
						{if $page}
							<a href="{plink $page}" class="btn btn--icon">
								<span class="btn__text">
									{if $btnText}
										{$btnText}
									{else}
										{$page->nameAnchor}
									{/if}
									{('bevel-right')|icon}
								</span>
							</a>
						{elseif $external && $btnText}
							<a href="{$external}" class="btn btn--icon" target="_blank" rel="noopener noreferrer">
								<span class="btn__text">
									{$btnText}
									{('bevel-right')|icon}
								</span>
							</a>
						{/if}
					</p>

				</div>
			</div>
		</div>
	</div>
</article>
