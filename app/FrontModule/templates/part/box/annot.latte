{default $spacingBottom = isset($spacing->mobile) ? $spacing->mobile : '13'}
{default $spacingBottomMd = isset($spacing->tablet) ? $spacing->tablet : '15'}
{default $spacingBottomLg = isset($spacing->desktop) ? $spacing->desktop : '17'}

{default $name = $object->name}
{if isset($object->uid) && $object->uid == 'articlesMain' && isset($tagValue) && $tagValue}
	{php $name = $object->name . ' - ' . $tagValue->value}
{/if}

<header class="b-annot u-mb-{$spacingBottom} u-mb-{$spacingBottomMd}@md u-mb-{$spacingBottomLg}@lg">
	<div class="grid grid--space-between grid--y-6 grid--y-8@md">
		<div class="grid__cell size--5-12@lg">
			<h1 class="u-mb-0">
				{if isset($seoLink) && $seoLink && $seoLink->nameTitle}
					{$seoLink->nameTitle}
				{elseif !empty($seoFilterCatalog->title)}
					{$seoFilterCatalog->title}
				{else}
					{$name}
				{/if}
			</h1>
		</div>
		<div n:ifcontent class="grid__cell grid__cell--middle size--5-12@lg u-mb-last-0">
			{if isset($seoLink) && $seoLink && $seoLink->annotation}
				<p class="b-annot__desc">
					{$seoLink->annotation}
				</p>
			{elseif !empty($seoFilterCatalog->annotation)}
				<p class="b-annot__desc">
					{$seoFilterCatalog->annotation}
				</p>
			{elseif isset($object->annotation) && $object->annotation}
				<p class="b-annot__desc">
					{$object->annotation|texy|noescape}
				</p>
			{elseif isset($object->cf->annotation) && $object->cf->annotation}
				<p class="b-annot__desc">
					{$object->cf->annotation|texy|noescape}
				</p>
			{/if}
		</div>
	</div>
</header>
