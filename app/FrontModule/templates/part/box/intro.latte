{default $categories = false}
{default $cf = false}
{default $type = false}
{default $imgs = $cf->imgs ?? false}
{default $video = isset($cf->video) ? $cf->video->getEntity() ?? false : false}
{default $poster = $cf->poster ?? false}
{default $btn = $cf->btn ?? false}

<header class="b-intro b-intro--hp u-mb-10 u-mb-14@md u-mb-19@lg">
	<div class="b-intro__bg img">
		{if $type == 'image'}
			{include '../core/picture.latte', imgs=>$imgs, type=>"intro"}
		{elseif $type == 'video'}
			<video autoplay loop muted playsinline {if $poster}poster="{$poster->getSize('lg')->src}"{/if}>
				<source src="{$video->url}" type="video/mp4">
			</video>
		{/if}
	</div>
	<div class="row-main">
		<div class="b-intro__content">
			<div class="b-intro__main u-mb-last-0">
				<h2 n:if="$object->name ?? false" class="b-intro__title h1 u-max-width-8-12 u-mx-auto">
					{$object->name|noescape}
				</h2>
				<p n:if="$object->annotation ?? false" class="b-intro__subtitle u-max-width-5-12 u-mx-auto">
					{$object->annotation|texy|noescape|replace:" "," "}
				</p>
				<p n:if="$btn" class="b-intro__btn">
					{define #pageLink}
						<a n:href="$page" class="btn btn--icon btn--tertiary btn--light">
							<span class="btn__text">
								{if isset($btn->text)}
									{$btn->text}
								{else}
									{$page->nameAnchor}
								{/if}
							</span>
						</a>
					{/define}

					{if isset($btn->page) && $btn->page && $btn->page->getEntity()}
						{include #pageLink, page: $btn->page}

					{elseif isset($btn->reference) && $btn->reference && $btn->reference->getEntity()}
						{include #pageLink, page: $btn->reference}

					{elseif isset($btn->event) && $btn->event && $btn->event->getEntity()}
						{include #pageLink, page: $btn->event}

					{elseif isset($btn->article) && $btn->article && $btn->article->getEntity()}
						{include #pageLink, page: $btn->article}

					{elseif isset($btn->external) && isset($btn->text)}
						<a href="{$btn->external}" class="btn btn--icon btn--tertiary btn--light" target="_blank" rel="noopener noreferrer">
							<span class="btn__text">
								{$btn->text}
								{('bevel-right')|icon}
							</span>
						</a>
					{/if}
				</p>
			</div>
			<p class="b-intro__down u-mb-0">
				<a href="#content" class="b-intro__down-link" data-controller="LinkSlide" data-action="LinkSlide#slideTo">
					{_"btn_look_more"}
					{('bevel-down')|icon}
				</a>
			</p>
		</div>
	</div>
</header>
