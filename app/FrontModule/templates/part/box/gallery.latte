{default $page = $object}
{default $cf = isset($page->cf) ? $page->cf : false}
{default $parentCf = ($page instanceof App\PostType\Core\Model\LocalizationEntity) && isset($page->getParent()->cf) ? $page->getParent()->cf : false }
{default $cc = isset($page->cc) ? $page->cc : false}
{default $tab = false}
{default $ccKey = null}

{if $cf || $parentCf || $cc}
	{php $gallery = []}
	{php $videos = []}
	{php $story = false}

	{* get data from intro *}
	{php $introMedia = $cf && isset($cf->intro_media) ? $cf->intro_media : false}
	{php $parentIntroMedia = $parentCf && isset($parentCf->intro_media) ? $parentCf->intro_media : false}
	{php $media = $introMedia ? $introMedia : $parentIntroMedia}
	{if $media && isset($media->youtube) && $media->youtube}
		{php $videos[] = [false, $media->youtube]}
	{elseif $media && isset($media->imgs) && isset($media->imgs->img_desktop) && $media->imgs->img_desktop}
		{php $gallery[] = [$page->name, $media->imgs->img_desktop]}
	{/if}

	{* get data from cc *}
	{foreach $cc as $key=>$item}
		{continueIf $ccKey !== null && $ccKey !== $key}

		{var $templateName = substr($key, 0, strpos($key, '____'))}
		{* {dump $item} *}

		{if $templateName == 'story'}{php $story = $item[0]}{/if}

		{if $templateName == 'photo' && isset($item[0]->src) && $item[0]->src}
			{php $gallery[] = [isset($item[0]->text) ? $item[0]->text : false, $item[0]->src]}
		{/if}

		{if $templateName == 'photos' && isset($item[0]->items)}
			{foreach $item[0]->items as $photosItem}
				{if isset($photosItem->image) && $photosItem->image}
 					{php $gallery[] = [isset($photosItem->content) ? $photosItem->content : false, $photosItem->image]}
				{/if}
			{/foreach}
		{/if}

		{if $templateName == 'materials' && isset($item[0]->items)}
			{foreach $item[0]->items as $materialItem}
				{if isset($materialItem->image) && $materialItem->image}
 					{php $gallery[] = [false, $materialItem->image]}
				{/if}
			{/foreach}
		{/if}

		{if $templateName == 'photos_half'}
			{if isset($item[0]->image1) && isset($item[0]->image1->image) && $item[0]->image1->image}
 				{php $gallery[] = [isset($item[0]->image1->alt) ? $item[0]->image1->alt : false, $item[0]->image1->image]}
			{/if}
			{if isset($item[0]->image2) && isset($item[0]->image2->image) && $item[0]->image2->image}
 				{php $gallery[] = [isset($item[0]->image2->alt) ? $item[0]->image2->alt : false, $item[0]->image2->image]}
			{/if}
		{/if}

		{if $templateName == 'photo_carousel' && isset($item[0]->items)}
			{foreach $item[0]->items as $photoItem}
				{if isset($photoItem->image) && isset($photoItem->image->src) && $photoItem->image->src}
					{php $gallery[] = [isset($photoItem->image->text) ? $photoItem->image->text: false, $photoItem->image->src]}
				{/if}
			{/foreach}
		{/if}

		{if $templateName == 'color_carousel' && isset($item[0]->items)}
			{foreach $item[0]->items as $color}
				{if isset($color->images)}
					{foreach $color->images as $colorImg}
						{php $gallery[] = [false, $colorImg]}
					{/foreach}
				{/if}
			{/foreach}
		{/if}

		{if $templateName == 'sections' && isset($item[0]->items)}
			{foreach $item[0]->items as $section}
				{if isset($section->image) && $section->image}
					{php $gallery[] = [isset($section->title) ? $section->title : false, $section->image]}
				{/if}
			{/foreach}
		{/if}

		{if $templateName == 'gallery' && isset($item[0]->images)}
			{foreach $item[0]->images as $galleryImg}
				{php $gallery[] = [false, $galleryImg]}
			{/foreach}
		{/if}

		{if $templateName == 'highlights' && isset($item[0]->items)}
			{foreach $item[0]->items as $highlight}
				{if isset($highlight->image) && $highlight->image}
					{php $gallery[] = [isset($highlight->title) ? $highlight->title : false, $highlight->image]}
				{/if}
			{/foreach}
		{/if}

		{if $templateName == 'grid_gallery' && isset($item[0]->gallery) && isset($item[0]->gallery->images)}
			{foreach $item[0]->gallery->images as $image}
				{php $gallery[] = [false, $image]}
			{/foreach}
		{/if}


		{if $templateName == 'img_full' && isset($item[0]->image) && $item[0]->image->getEntity()}
			{php $gallery[] = [isset($item[0]->alt) ? $item[0]->alt : false, $item[0]->image]}
		{/if}

		{if $templateName == 'videos' && isset($item[0]->items)}
			{foreach $item[0]->items as $videoItem}
				{if isset($videoItem->link) && $videoItem->link}
					{php $videos[] = [false, $videoItem->link]}
				{/if}
			{/foreach}
		{/if}

		{if $templateName == 'carousel' && isset($item[0]->images)}
			{foreach $item[0]->images as $img}
				{php $gallery[] = [false, $img]}
			{/foreach}
		{/if}

		{if $templateName == 'content_photo' && isset($item[0]->image) && isset($item[0]->image->src) && $item[0]->image->src}
			{php $gallery[] = [isset($item[0]->image->text) ? $item[0]->image->text : false, $item[0]->image->src]}
		{/if}

		{if $templateName == 'photo_content' && isset($item[0]->image) && isset($item[0]->image->src) && $item[0]->image->src}
			{php $gallery[] = [isset($item[0]->image->text) ? $item[0]->image->text : false, $item[0]->image->src]}
		{/if}

		{if $templateName == 'images' && isset($item[0]->images)}
			{foreach $item[0]->images as $img}
				{php $gallery[] = [false, $img]}
			{/foreach}
		{/if}

		{if $templateName == 'design_info' && isset($item[0]->image) && isset($item[0]->image->src) && $item[0]->image->src}
			{php $gallery[] = [false, $item[0]->image->src]}
		{/if}

		{if $templateName == 'design_steps' && isset($item[0]->items)}
			{foreach $item[0]->items as $designStep}
				{if isset($designStep->carousels)}
					{foreach $designStep->carousels as $stepCarousel}
						{if isset($stepCarousel->images)}
							{foreach $stepCarousel->images as $stepImg}
								{php $gallery[] = [false, $stepImg]}
							{/foreach}
						{/if}
					{/foreach}
				{/if}

			{/foreach}
		{/if}

		{if $templateName == 'lighting'}
			{if isset($item[0]->imageLight) && $item[0]->imageLight}
				{php $gallery[] = [false, $item[0]->imageLight]}
			{/if}
			{if isset($item[0]->imageDark) && $item[0]->imageDark}
				{php $gallery[] = [false, $item[0]->imageDark]}
			{/if}
		{/if}
	{/foreach}

	{* set active tab (if it's not already set) *}
	{if count($gallery) && !$tab}
		{php $tab = 'gallery'}
	{elseif $story && !$tab}
		{php $tab = 'story'}
	{elseif count($videos) && !$tab}
		{php $tab = 'video'}
	{/if}

	<div class="b-gallery has-navigation" data-controller="Gallery">
		<div class="b-gallery__sliders">
			<div class="row-main">
				<div class="b-gallery__main">
					{* Gallery *}
					<div n:if="count($gallery)" n:class="b-gallery__slider, $tab == 'gallery' ? is-active" data-Gallery-target="slider" data-embla-tab>
						<div class="embla" data-controller="Embla">
							<div class="embla__viewport" data-embla-target="viewport" data-action="Gallery:tabChange@window->Embla#update Gallery:slideChange@window->Embla#goTo Embla:emblaChange@window->Gallery#setActiveThumb">
								<ul class="grid embla__container grid--y-0 grid--nowrap grid--scroll">
									{foreach $gallery as $item}
										{php $img = $item[1]}
										{if $img instanceof App\Model\CustomField\LazyValue}
											{php $img = $item[1]??->getEntity()}
										{/if}
										<li n:if="$img" class="embla__slide grid__cell">
											<div class="b-gallery__img img img--contain">
												<img src="{$img->getSize('section')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
											</div>
										</li>
									{/foreach}
								</ul>
							</div>
							<div class="b-gallery__btns embla__btns">
								<button type="button" class="embla__btn embla__btn--prev" data-action="Embla#prev" data-embla-target="prevButton">
									<span class="u-vhide">
										{_"previous"}
									</span>
									{('angle-left')|icon}
								</button>
								<button type="button" class="embla__btn embla__btn--next" data-action="Embla#next" data-embla-target="nextButton">
									<span class="u-vhide">
										{_"next"}
									</span>
									{('angle-right')|icon}
								</button>
							</div>
							<div class="embla__navigation">
								<span data-embla-target="index">1</span>
								/
								<span data-embla-target="length">{count($gallery)}</span>
							</div>
						</div>
					</div>

					{* Stories *}
					<div n:if="$story && count($story->items)" n:class="b-gallery__slider, $tab == 'story' ? is-active" data-Gallery-target="slider" data-embla-parenttab>
						<div n:foreach="$story->items as $storyGroup" n:if="isset($storyGroup->name) && isset($storyGroup->items)" n:class="b-gallery__slider, $iterator->first ? is-active" data-Gallery-target="storiesSlider" data-embla-tab>
							<div class="embla" data-controller="Embla">
								<div class="embla__viewport" data-embla-target="viewport" data-action="Gallery:tabChange@window->Embla#update Gallery:slideChange@window->Embla#goTo Embla:emblaChange@window->Gallery#setActiveThumb">
									<ul class="grid embla__container grid--y-0 grid--nowrap grid--scroll">
										<li n:foreach="$storyGroup->items as $item" class="embla__slide grid__cell">
											{php $storyTitle = $item->title ?? false}
											{php $storyImgSrc = $item->image ?? false && $item->image->getEntity() ? $item->image->getSize('section')->src : '/static/img/illust/noimg.svg'}
											{php $storyAlt = $item->image ?? false && $item->image->getEntity() ? $item->image->getAlt($mutation) : ''}
											{php $storyContent = $item->content ?? false}
											{if $storyImgSrc && $storyContent}
												<div class="b-gallery__img b-story__item">
													<div class="b-story__content u-mb-last-0" n:ifcontent>
														<h4 n:if="$storyTitle" class="b-story__title-sub h4 u-mb-4">
															{$storyTitle}
														</h4>
														<p n:ifcontent>
															{$storyContent}
														</p>
													</div>
													<div class="b-story__img">
														<div class="img img--contain">
															<img src="{$storyImgSrc}" alt="{$storyAlt}" loading="lazy">
														</div>
													</div>
												</div>
											{elseif $storyImgSrc}
												<div class="b-gallery__img img img--contain">
													<img src="{$storyImgSrc}" alt="{$storyAlt}" loading="lazy">
												</div>
											{elseif $storyContent}
												<p class="u-mb-0">
													{$storyContent}
												</p>
											{/if}
										</li>
									</ul>
								</div>
								<div class="b-gallery__btns embla__btns">
									<button type="button" class="embla__btn embla__btn--prev" data-action="Embla#prev" data-embla-target="prevButton">
										<span class="u-vhide">
											{_"previous"}
										</span>
										{('angle-left')|icon}
									</button>
									<button type="button" class="embla__btn embla__btn--next" data-action="Embla#next" data-embla-target="nextButton">
										<span class="u-vhide">
											{_"next"}
										</span>
										{('angle-right')|icon}
									</button>
								</div>
								<div class="embla__navigation">
									<span data-embla-target="index">1</span>
									/
									<span data-embla-target="length">{count($storyGroup->items)}</span>
								</div>
							</div>
						</div>
					</div>

					{* Video *}
					<div n:if="count($videos)" n:class="b-gallery__slider, $tab == 'video' ? is-active" data-Gallery-target="slider" data-embla-tab>
						<div class="embla" data-controller="Embla">
							<div class="embla__viewport" data-embla-target="viewport" data-action="Gallery:tabChange@window->Embla#update Gallery:slideChange@window->Embla#goTo Embla:emblaChange@window->Gallery#setActiveThumb">
								<ul class="grid embla__container grid--y-0 grid--nowrap grid--scroll">
									<li n:foreach="$videos as $item" class="embla__slide grid__cell">
										{include $templates.'/part/core/video.latte', link=>$item[1], dataAction=>"Embla:emblaChange@window->Video#stopOnSlides"}
									</li>
								</ul>
							</div>
							<div class="b-gallery__btns embla__btns">
								<button type="button" class="embla__btn embla__btn--prev" data-action="Embla#prev" data-embla-target="prevButton">
									<span class="u-vhide">
										{_"previous"}
									</span>
									{('angle-left')|icon}
								</button>
								<button type="button" class="embla__btn embla__btn--next" data-action="Embla#next" data-embla-target="nextButton">
									<span class="u-vhide">
										{_"next"}
									</span>
									{('angle-right')|icon}
								</button>
							</div>
							<div class="embla__navigation">
								<span data-embla-target="index">1</span>
								/
								<span data-embla-target="length">{count($videos)}</span>
							</div>
						</div>
					</div>

					{* Enlarge btn *}
					<button type="button" class="b-gallery__enlarge as-link" data-controller="ToggleClass" data-action="ToggleClass#toggle" data-toggle-class="has-navigation" data-toggle-class-content=".b-gallery">
						{('fullscreen-exit')|icon}
						{('fullscreen')|icon}
						<span class="u-vhide">
							{_"enlarge"}
						</span>
					</button>
				</div>
			</div>
		</div>
		<p class="content-indented b-gallery__tabs">
			<button n:if="count($gallery)" type="button" n:class="b-gallery__link, as-link, $tab == 'gallery' ? is-active" data-action="Gallery#switchTab" data-Gallery-target="tab">
				{_"gallery"}
			</button>
			<button n:if="$story" type="button" n:class="b-gallery__link, as-link, $tab == 'story' ? is-active" data-action="Gallery#switchTab" data-Gallery-target="tab">
				{_"gallery"}
			</button>
			<button n:if="count($videos)" type="button" n:class="b-gallery__link, as-link, $tab == 'video' ? is-active" data-action="Gallery#switchTab" data-Gallery-target="tab">
				{_"video"}
			</button>
		</p>
		<div class="b-gallery__thumbs-wrap">
			<div class="row-main">
				{* Gallery *}
				<div n:if="count($gallery)" n:class="b-gallery__thumbs, b-gallery__thumbs--nav, $tab == 'gallery' ? is-active" data-Gallery-target="thumbs">
					<div class="embla" data-controller="Embla" data-Embla-settings-value='{"inViewThreshold": 1}'>
						<div class="embla__viewport" data-embla-target="viewport" data-action="Gallery:itemChange@window->Embla#checkActiveSlide">
							<ul class="grid embla__container grid--x-6 grid--x-6@md grid--x-6@md grid--y-0 grid--nowrap grid--scroll">
								{foreach $gallery as $item}
									{if $item[1] ?? false}
										{if $item[1] instanceof App\Model\CustomField\LazyValue}
											{php $img = $item[1]->getEntity() ?? false}
										{else}
											{php $img = $item[1]}
										{/if}

										{dump $item[0]}

										<li n:if="$img" n:class="b-gallery__thumbitem, embla__slide, grid__cell, size--auto, $iterator->first ? is-active" data-Gallery-target="thumbItem">
											<button type="button" class="b-gallery__thumblink as-link" data-action="Gallery#goTo">
												<span class="b-gallery__img img">
													<img src="{$img->getSize('sm')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
													<span n:if="$item[0]" class="b-gallery__thumbname">
														{$item[0]}
													</span>
												</span>
											</button>
										</li>
									{/if}
								{/foreach}
							</ul>
						</div>
						<div class="b-gallery__btns embla__btns">
							<button type="button" class="embla__btn embla__btn--prev" data-action="Embla#prev" data-embla-target="prevButton">
								<span class="u-vhide">
									{_"previous"}
								</span>
								{('angle-left')|icon}
							</button>
							<button type="button" class="embla__btn embla__btn--next" data-action="Embla#next" data-embla-target="nextButton">
								<span class="u-vhide">
									{_"next"}
								</span>
								{('angle-right')|icon}
							</button>
						</div>
					</div>
				</div>

				{* Stories *}
				<div n:if="$story && count($story->items)" n:class="b-gallery__thumbs, $tab == 'story' ? is-active" data-Gallery-target="thumbs">
					<div class="b-tabs" data-controller="Etarget">
						<div class="b-tabs__tabs">
							<button type="button" class="b-tabs__btn select__btn inp-select inp-select--underline" data-controller="ToggleClass" data-action="ToggleClass#toggle" data-toggle-class="is-open" data-toggle-class-content=".b-tabs">
								{_"btn_select_section"}
							</button>
							<ul class="b-tabs__list">
								<li n:foreach="$story->items as $storyGroup" n:if="isset($storyGroup->name) && isset($storyGroup->items)" n:class="b-tabs__item, $iterator->first ? is-active" data-Gallery-target="storiesTab">
									<button type="button" class="b-tabs__link as-link" data-action="Gallery#switchStoriesTab">
										{$storyGroup->name}
									</button>
								</li>
							</ul>
						</div>
						<div class="b-tabs__wrap">
							<div n:foreach="$story->items as $storyGroup" n:if="isset($storyGroup->name) && isset($storyGroup->items)"  n:class="b-gallery__thumbs, b-gallery__thumbs--nav, $iterator->first ? is-active" data-Gallery-target="storiesThumbs">
								<div class="embla" data-controller="Embla" data-Embla-settings-value='{"inViewThreshold": 1}'>
									<div class="embla__viewport" data-embla-target="viewport">
										<ul class="grid embla__container grid--x-6 grid--x-6@md grid--y-0 grid--nowrap grid--scroll">
											<li n:foreach="$storyGroup->items as $item" n:if="count($storyGroup->items) > 1" n:class="b-gallery__thumbitem, embla__slide, grid__cell, size--auto, $iterator->first ? is-active" data-Gallery-target="thumbItem">
												{php $storyTitle = isset($item->title) ? $item->title : false}
												{php $storyImgSrc = isset($item->image) ? $item->image->getSize('sm')->src : '/static/img/illust/noimg.svg'}
												{php $storyImgAlt = isset($item->image) ? $item->image->getAlt($mutation) : ''}
												{php $storyContent = isset($item->content) ? $item->content : false}

												<button type="button" class="b-gallery__thumblink as-link" data-action="Gallery#goTo">
													<span class="b-gallery__img img">
														{if $storyImgSrc}
															<img src="{$storyImgSrc}" alt="{$storyImgAlt}" loading="lazy">
														{/if}
														<span n:if="$storyTitle" class="b-gallery__thumbname">
															{$storyTitle}
														</span>
													</span>
												</button>
											</li>
										</ul>
									</div>
									<div class="b-gallery__btns embla__btns">
										<button type="button" class="embla__btn embla__btn--prev" data-action="Embla#prev" data-embla-target="prevButton">
											<span class="u-vhide">
												{_"previous"}
											</span>
											{('angle-left')|icon}
										</button>
										<button type="button" class="embla__btn embla__btn--next" data-action="Embla#next" data-embla-target="nextButton">
											<span class="u-vhide">
												{_"next"}
											</span>
											{('angle-right')|icon}
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				{* Video *}
				<div n:if="count($videos)" n:class="b-gallery__thumbs, b-gallery__thumbs--nav, $tab == 'video' ? is-active" data-Gallery-target="thumbs">
					<div class="embla" data-controller="Embla" data-Embla-settings-value='{"inViewThreshold": 1}'>
						<div class="embla__viewport" data-embla-target="viewport">
							<ul class="grid embla__container grid--x-6 grid--x-6@md grid--x-6@md grid--y-0 grid--nowrap grid--scroll">
								<li n:foreach="$videos as $item" n:class="b-gallery__thumbitem, embla__slide, grid__cell, size--auto, $iterator->first ? is-active" data-Gallery-target="thumbItem">
									<button type="button" class="b-gallery__thumblink as-link" data-action="Gallery#goTo">
										<span class="b-gallery__img img">
											{include $templates.'/part/core/video-thumb.latte', link=>$item[1], width=>'270'}
											{include $templates.'/part/core/play.latte', class: false}
											<span n:if="$item[0]" class="b-gallery__thumbname">
												{$item[0]}
											</span>
										</span>
									</button>
								</li>
							</ul>
						</div>
						<div class="b-gallery__btns embla__btns">
							<button type="button" class="embla__btn embla__btn--prev" data-action="Embla#prev" data-embla-target="prevButton">
								<span class="u-vhide">
									{_"previous"}
								</span>
								{('angle-left')|icon}
							</button>
							<button type="button" class="embla__btn embla__btn--next" data-action="Embla#next" data-embla-target="nextButton">
								<span class="u-vhide">
									{_"next"}
								</span>
								{('angle-right')|icon}
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{/if}
