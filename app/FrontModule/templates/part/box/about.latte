{default $item = false}

{if $item}
	{var $pressMutation = $item}
	{var $press = $pressMutation->getParent()}

	{var $pressCf = isset($press->cf) ? $press->cf : false}
	{var $pressMutationCf = isset($pressMutation->cf) ? $pressMutation->cf : false}
	{php $annot = isset($pressMutationCf->annotation) ? $pressMutationCf->annotation : false}
	{php $imgSrc = isset($pressCf->img_crossroad) ? $pressCf->img_crossroad->getSize('xs')->src : '/static/img/illust/noimg.svg'}
	{php $imgAlt = isset($pressCf->img_crossroad) ? $pressCf->img_crossroad->getAlt($mutation) : ''}
	{php $category = isset($pressMutation->categories) ? $pressMutation->categories->toCollection()->fetch() : false}

	<article class="b-about link-extend">
		<div class="b-about__img">
			<img src="{$imgSrc}" alt="{$imgAlt}" loading="lazy">
		</div>
		<h3 class="b-about__title u-mb-4 h5 u-mt-8  u-mt-9@lg">
			<a href="{plink $pressMutation}" class="b-event__link link-extend__link">
				{$pressMutation->nameAnchor}
			</a>
		</h3>
		<p n:if="$annot" class="u-mb-5 u-mb-8@lg">
			{$annot}
		</p>
		<p class="b-about__btns">
			<a n:if="$category" href="{plink $category}" class="btn btn--tertiary link-extend__unmask">
				<span class="btn__text">
					{$category->nameAnchor}
				</span>
			</a>
			<a href="{plink $pressMutation}" class="btn btn--icon btn--tertiary link-extend__unmask{if !$category} u-ml-auto{/if}">
				<span class="btn__text">
					{_"btn_read_more"}
					<span class="u-vhide">
						{_about} {$category->nameAnchor}
					</span>
					{('bevel-right')|icon}
				</span>
			</a>
		</p>
	</article>
{/if}
