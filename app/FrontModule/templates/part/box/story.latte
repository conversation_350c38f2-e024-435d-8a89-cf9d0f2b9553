{default $spacingBottom = isset($spacing->mobile) ? $spacing->mobile : '10'}
{default $spacingBottomMd = isset($spacing->tablet) ? $spacing->tablet : '14'}
{default $spacingBottomLg = isset($spacing->desktop) ? $spacing->desktop : '26'}
{default $title = false}
{default $subtitle = false}
{default $btn = false}
{default $items = []}

<section n:if="count($items) > 0" class="b-story u-mb-{$spacingBottom} u-mb-{$spacingBottomMd}@md u-mb-{$spacingBottomLg}@lg">
	<div n:if="$title || $subtitle" class="content-indented b-story__title u-mb-last-0 u-mb-9 u-mb-11@md">
		<p n:if="$subtitle" class="title subtitle u-mb-4">
			{$subtitle}
		</p>
		<h2 n:if="$title" class="h3 u-mt-0">
			{$title}
		</h2>
	</div>

	<div class="b-story__tabs tabs" data-controller="Tabs">
		<div class="b-story__nav tabs__navigation" data-controller="Etarget">
			<button type="button" class="tabs__dropdown" data-controller="ToggleClass" data-action="ToggleClass#toggle" data-toggle-class="is-open" data-toggle-class-content=".tabs__navigation">
				{_"choose_item"}
			</button>
			<ul class="tabs__list">
				{foreach $items as $item}
					<li n:if="isset($item->name)" class="tabs__item{if $iterator->isFirst()} is-selected{/if}" data-Tabs-target="item">
						<a href="#story_tab_{$iterator->counter}" class="tabs__link" data-Tabs-target="link" data-controller="ToggleClass" data-action="Tabs#changeTab ToggleClass#toggle" data-toggle-class="is-open" data-toggle-class-content=".tabs__navigation">
							{$item->name}
						</a>
					</li>
				{/foreach}
			</ul>
		</div>
		{foreach $items as $item}
			<div n:if="isset($item->name)" id="story_tab_{$iterator->counter}" class="tabs__fragment{if $iterator->isFirst()} is-active{/if}" data-Tabs-target="content">
				<h3 class="u-vhide">
					{$item->name}
				</h3>
				<div n:if="isset($item->items)" class="b-story__list">
					{foreach $item->items as $block}
						{php $reverse = $block->reverse ?? false}
						{php $rowspan = $block->rowspan ?? false}
						{php $colspan = $block->colspan ?? false}
						{php $title = $block->title ?? false}
						{php $content = $block->content ?? false}
						{php $link = $block->link ?? false}

						{capture $content}
							<div n:ifcontent class="b-story__content u-mb-last-0">
								<h4 n:if="$title" class="b-story__title-sub h5 u-mb-4">
									{$title}
								</h4>
								<p n:if="$content">
									{$content}
								</p>
								<p n:if="$link" n:ifcontent class="u-mt-6">
									{if $link->page ?? false}
										<a href="{plink $link->page}" class="btn btn--tertiary btn--icon">
											<span class="btn__text">
												{if isset($link->text)}
													{$link->text}
												{else}
													{$link->page->nameAnchor}
												{/if}
												{('bevel-right')|icon}
											</span>
										</a>
									{elseif ($link->external ?? false) && ($link->text ?? false)}
										<a href="{$link->external}" class="btn btn--tertiary btn--icon" target="_blank" rel="noopener noreferrer">
											<span class="btn__text">
												{$link->text}
												{('bevel-right')|icon}
											</span>
										</a>
									{/if}
								</p>
							</div>
						{/capture}

						<article n:class="b-story__item, !$content ? b-story__item--no-content, $reverse ? b-story__item--reverse, $rowspan ? b-story__item--rowspan-2, $colspan ? b-story__item--colspan-2">
							{$content}
							{php $isXxl = ($rowspan && $colspan) || ($colspan && count($item->items) == 1)}
							{php $isHalfImage = !$colspan && $rowspan && !$content}
							<div n:if="$block->image ?? false && $block->image->getEntity()" class="b-story__img">
								<div n:class="img, img--contain, $isXxl ? img--16-9">
									{php $imgSize = $isXxl ? 'xxl-16-9' : 'lg'}
									{php $img = $block->image->getSize($imgSize)}
									<img src="{$img->src}" alt="{$block->image->getAlt($mutation)}" loading="lazy">
								</div>
							</div>
						</article>
					{/foreach}
				</div>
			</div>
		{/foreach}
	</div>

	{if $btn}
		{include $templates.'/part/core/btn-wrap.latte', btn=>$btn, spacingBottom=>0, spacingBottomMd=>0, spacingBottomLg=>0}
	{/if}
</section>

