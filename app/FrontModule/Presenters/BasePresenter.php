<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters;

use App\Exceptions\LogicException;
use App\FrontModule\Components\Breadcrumb\Breadcrumb;
use App\FrontModule\Components\Breadcrumb\BreadcrumbFactory;
use App\FrontModule\Components\CanonicalUrl\CanonicalUrl;
use App\FrontModule\Components\CanonicalUrl\CanonicalUrlFactory;
use App\FrontModule\Components\ContactForm\ContactForm;
use App\FrontModule\Components\ContactForm\ContactFormFactory;
use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Components\EditButton\EditButton;
use App\FrontModule\Components\EditButton\EditButtonFactory;
use App\FrontModule\Components\Favourite\Favourite;
use App\FrontModule\Components\Favourite\FavouriteFactory;
use App\FrontModule\Components\Menu\Menu;
use App\FrontModule\Components\Menu\MenuFactory;
use App\FrontModule\Components\Modal\Modal;
use App\FrontModule\Components\Modal\ModalFactory;
use App\FrontModule\Components\NewsletterForm\NewsletterForm;
use App\FrontModule\Components\NewsletterForm\NewsletterFormFactory;
use App\FrontModule\Components\Rating\Rating;
use App\FrontModule\Components\Rating\RatingFactory;
use App\FrontModule\Components\Robots\Robots;
use App\FrontModule\Components\Robots\RobotsFactory;
use App\FrontModule\Components\SignInForm\SignInForm;
use App\FrontModule\Components\SignInForm\SignInFormFactory;
use App\FrontModule\Components\ToggleLanguage\ToggleLanguage;
use App\FrontModule\Components\ToggleLanguage\ToggleLanguageFactory;
use App\FrontModule\Components\UserMenu\UserMenu;
use App\FrontModule\Components\UserMenu\UserMenuFactory;
use App\FrontModule\Presenters\Homepage\HomepagePresenter;
use App\Infrastructure\BasicAuth\Authenticator;
use App\Infrastructure\Latte\Filters;
use App\Model\CustomField\LazyValue;
use App\Model\ImageResizerWrapper;
use App\Model\Link\LinkFactory;
use App\Model\Link\LinkSeo;
use App\Model\Mutation\MutationDetector;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Routable;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\State\State;
use App\PostType\Page\Model\Orm\TreeModel;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\PostType\Core\Model\LocalizationEntity;
use Nette;
use Nette\Application\Attributes\Persistent;
use Nette\Application\Request;
use Nette\Application\UI\Multiplier;
use Nette\Caching\Cache;
use Nette\DI\Attributes\Inject;
use Nextras\Orm\Entity\IEntity;
use function assert;
use App\Model\GeoHelper\IpLocation;

abstract class BasePresenter extends \App\BasePresenter
{
	use HasCustomContentRenderer;

	#[Persistent]
	public string $alias;

	#[Inject]
	public Nette\Http\RequestFactory $requestFactory;

	#[Inject]
	public ToggleLanguageFactory $toggleLanguageFactory;

	#[Inject]
	public Nette\Http\Session $session;

	#[Inject]
	public Nette\Caching\Storage $cacheStorage;

	#[Inject]
	public FavouriteFactory $favouriteFactory;

	#[Inject]
	public TranslatorDB $translator;

	#[Inject]
	public MutationHolder $mutationHolder;

	#[Inject]
	public MutationDetector $mutationDetector;

	#[Inject]
	public LinkFactory $linkFactory;

	#[Inject]
	public ImageResizerWrapper $imageResizerWrapper;

	#[Inject]
	public ContactFormFactory $contactFormFactory;

	#[Inject]
	public NewsletterFormFactory $newsletterFormFactory;

	#[Inject]
	public SignInFormFactory $signInFormFactory;

	#[Inject]
	public BreadcrumbFactory $breadcrumbFactory;

	#[Inject]
	public MenuFactory $menuFactory;

	#[Inject]
	public UserMenuFactory $userMenuFactory;
	#[Inject]
	public RatingFactory $ratingFactory;

	#[Inject]
	public ModalFactory $modalFactory;

	#[Inject]
	public RobotsFactory $robotsFactory;

	#[Inject]
	public CanonicalUrlFactory $canonicalUrlFactory;

	#[Inject]
	public TreeModel $treeModel;

	#[Inject]
	public EditButtonFactory $editButtonFactory;
	#[Inject]
	public LinkSeo $linkSeo;

	protected Routable|StaticPage $object;

	protected Mutation $mutation;

	protected Cache $cache;

	/**
	 * state = kolize s persistant v NewsletterPresenter
	 */
	protected State $currentState;

	protected PriceLevel $priceLevel;

	#[Inject]
	public Authenticator $basicAuth;

	protected bool $bypassBasicAuth = false;

	protected bool $enforcedTemplateForGallery = false;

	/** @inject */
	public IpLocation $ipLocation;

	protected function startup(): void
	{
		// ******* redirects ************************
		if (isset($_GET['terminate'])) {
			$this->terminate();
		}

		if ($this->request->hasFlag(Request::RESTORED)) { // reseni pro backlink po ajaxu
			$this->redirect('this');
		}

		// ******* basic ************************
		parent::startup();

		$this->dbaLog->register();
		$this->setMutation();
		$this->setState();
		$this->setPriceLevel();
		$this->cache = new Cache($this->cacheStorage);

//		$this->autoCanonicalize = FALSE;

		// ******* helpers & templates ************************
		Filters::$mutation = $this->mutationHolder->getMutation();
		Filters::$translator = $this->translator;
		Filters::$version = $this->configService->get('webVersion');

		// prepsani defaultnich hlasek
		Nette\Forms\Validator::$messages[Nette\Forms\Form::EMAIL] = 'form_valid_email';
		Nette\Forms\Validator::$messages[Nette\Forms\Form::FILLED] = 'form_error';

		// ******* other ************************
	}

	public function checkRequirements(mixed $element): void
	{
		parent::checkRequirements($element);

		if ( ! $element instanceof Nette\Application\UI\ComponentReflection) {
			return;
		}

		if ($this->bypassBasicAuth) {
			return;
		}

		$this->basicAuth->authenticate(
			$this->getHttpRequest(),
			function (): never {
				$this->getHttpResponse()->setHeader('WWW-Authenticate', 'Basic realm="app"');
				$this->error(httpCode: Nette\Http\IResponse::S401_UNAUTHORIZED);
			},
		);
	}

	protected function setMutation(): void
	{
		if ($this->getParameter('mutation')) {
			$this->mutation = $this->getParameter('mutation');
		} else {
			$this->mutation = $this->mutationDetector->detect();
		}

		$this->mutationHolder->setMutation($this->mutation);
		$this->orm->setMutation($this->mutation);
	}


	/**
	 * Urceni statu podle cookie (user si nekde zvolil stat)
	 * nebo defaultne prvni stat mutace
	 *
	 * @throws LogicException
	 */
	protected function setState(): void
	{
		$currentState = null;
		$stateCode = (string)$this->getHttpRequest()->getCookie(State::COOKIE_NAME_SELECTED_STATE);
		if (!empty($stateCode)) {
			$currentState = $this->orm->state->getByCode($stateCode);
		} else {
			$stateCode = $this->ipLocation->detectStateCode($this->mutation);
			if ($stateCode) {
				$currentState = $this->orm->state->getByCode($stateCode);
			}
		}

		if (!$currentState) {
			$currentState = $this->mutation->states->toCollection()->fetch();
		}

		if (!$currentState) {
			throw new LogicException('Unknown current state');
		}

		// bd("curr state", $currentState);
		$this->currentState = $currentState;
	}

	protected function setPriceLevel(): void
	{
		$priceLevelId = $this->userEntity?->priceLevel->id ?? PriceLevel::DEFAULT_ID;
		$this->priceLevel = $this->orm->priceLevel->getById($priceLevelId);
	}

	protected function beforeRender(): void
	{
		parent::beforeRender();

		// ******* basic ************************
		$this->template->setTranslator($this->translator);
		$this->template->mutation = $this->mutationHolder->getMutation();
		$this->template->imageObjectFactory = $this->imageObjectFactory;
		$this->layout = FE_TEMPLATE_DIR . '/@layout.latte';
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->isHomepage = $this instanceof HomepagePresenter;
		//  robots per mutation
		if ($this->mutation->langCode) {
			$this->template->currencyCode = $this->mutation->currency; //"CZK";
		}

		$this['newsletterForm']->prepareAntispam();
		$this['contactForm']->prepareAntispam();

		$this->template->object = $this->getObject();
		$this->template->pages = $this->mutationHolder->getMutation()->pages;
		$this->template->state = $this->currentState;
		$this->template->priceLevel = $this->priceLevel;
		$this->template->translator = $this->translator;

		/** Preciosa */
		$contactByState = $this->orm->contactLocalization->getByState($this->currentState, $this->mutation);
		$mainContactPerson = null;
		if ($contactByState !== null) {
			$mainContactPerson = $this->orm->personLocalization->getByOffice($this->mutation, $contactByState);
		}
		$this->template->mainContactPerson = $mainContactPerson;
		$this->template->news = new \ArrayIterator($this->orm->blogLocalization->getNewest($this->mutation)->fetchAll());
		$this->template->references = new \ArrayIterator($this->orm->referenceLocalization->getNewest($this->mutation)->fetchAll());
		$this->template->contactsAll = $this->orm->contactLocalization->findBy(['mutation->id' => $this->mutation->id]);
		$this->template->mostUsedBlogTags = $this->orm->blogTagLocalization->getTagsOrderedByUse($this->mutation);
		$this->template->linkSeo = $this->linkSeo;


		// ******* callbacks ********************
		$this->template->getImage = function ($entity, $size) {
			return $this->imageResizerWrapper->getResizedImage($entity, $size);
		};

		$this->template->cfg = function () {
			return call_user_func_array([$this->configService, 'get'], func_get_args());
		};

		// ******* other ********************
		if (isset($this->object->template) && $this->object->template) {
			$_presenterName = explode(':', $this->object->template);
			if (isset($_presenterName[0])) {
				if (isset($this->object->parent) && $this->object->parent === null) {
					$this->template->presenterName = 'Homepage';
				} else {
					$this->template->presenterName = $_presenterName[0];
				}
			}
		}

		$this->template->googleAnalyticsCode = $this->mutationHolder->getMutation()->getGACode();
		$this->template->googleApiKey = $this->configService->getParam('google', 'apiKey');

		$this->template->hrefLangs = $this->getHrefLangs();

		if ($this->request->getParameter('galerie') !== null) {
			$this->template->setFile(FE_TEMPLATE_DIR.'/universal/galerie.latte');
			$this->enforcedTemplateForGallery = true;
			$this->template->ccKey = $this->request->getParameter('ccKey');

			if ($this->isAjax()) {
				$this->redrawControl();
			}
		}
	}

	protected function handleMeasureIp(): void
	{
		$this->template->measureIp = base64_encode($this->configService->get('REMOTE_ADDR'));

		if ($this->userEntity && isset($this->userEntity->id)) {
			$this->template->measureUserId = $this->userEntity->id;
			if ($this->userEntity->createdTime) {
				$this->template->measureUserCreated = $this->userEntity->createdTime->format('Y-d-m');
			} else {
				$this->template->measureUserCreated = null;
			}
		} else {
			$this->template->measureUserId = null;
			$this->template->measureUserCreated = null;
		}
	}


	public function setObject(Routable|StaticPage $object): void
	{
		$this->object = $object;
	}


	public function getObject(): Routable|StaticPage
	{
		return $this->object;
	}


	public function handleLogout(): void
	{
		$this->getUser()->logout(true);
		$this->flashMessage('msg_info_logout');
		$this->redirect('this');
	}


	// ************************** COMPONENTS ****************************** /

	protected function createComponentBreadcrumb(): Breadcrumb
	{
		return $this->breadcrumbFactory->create($this->getObject());
	}

	protected function createComponentSignInForm(): SignInForm
	{
		return $this->signInFormFactory->create($this->mutation, $this->object);
	}

	protected function createComponentSignInFormHeader(): SignInForm
	{
		return $this->signInFormFactory->create($this->mutation, $this->object);
	}

	protected function createComponentMenu(): Menu
	{
		$object = $this->getObjectForComponent($this->object);
		assert($object instanceof IEntity);
		return $this->menuFactory->create($object, $this->mutation);
	}

	protected function createComponentUserMenu(): UserMenu
	{
		$object = $this->getObjectForComponent($this->object);
		assert($object instanceof RoutableEntity);
		return $this->userMenuFactory->create($object);
	}

	protected function createComponentModal(): Modal
	{
		return $this->modalFactory->create($this->mutation, $this->currentState);
	}

	protected function createComponentContactForm(): ContactForm
	{
		$object = $this->getObjectForComponent($this->object);
		assert($object instanceof IEntity);
		return $this->contactFormFactory->create($object);
	}

	protected function createComponentNewsletterForm(): NewsletterForm
	{
		return $this->newsletterFormFactory->create();
	}

	protected function createComponentCanonicalUrl(): CanonicalUrl
	{
		return $this->canonicalUrlFactory->create();
	}


	protected function createComponentRobots(): Robots
	{
		return $this->robotsFactory->create($this->getObject(), $this->mutationHolder->getMutation());
	}


	protected function createComponentEditButton(): EditButton
	{
		$routable = $this->getObject();
		if ($routable instanceof Routable) {
			return $this->editButtonFactory->create($routable, $this->userEntity);
		} else {
			return $this->editButtonFactory->create(null, $this->userEntity);
		}

	}


	public function createComponentToggleLanguage(): ToggleLanguage
	{
		$localizationEntity = $this->getObjectForComponent($this->object);
		assert($localizationEntity instanceof LocalizationEntity);

		return $this->toggleLanguageFactory->create($localizationEntity);
	}


	// ****************************** INTERNALS ****************************** /

	/**
	 * @param array $args
	 * @throws Nette\Application\UI\InvalidLinkException
	 */
	public function link(Product|LazyValue|Routable|string $destination, $args = []): string
	{
		[$destination, $args] = $this->translateDestination($destination, $args);

		if ($destination instanceof Routable) {
			[$destination, $args] = $this->linkFactory->linkInPresenter($destination, $args);
		}

		return parent::link($destination, $args);
	}


	/**
	 * @throws Nette\Application\AbortException
	 */
	public function redirect(Product|LazyValue|Routable|string $destination, $args = []): never
	{
		[$destination, $args] = $this->translateDestination($destination, $args);

		if ($destination instanceof Routable) {
			[$destination, $args] = $this->linkFactory->linkInPresenter($destination, $args);
		}

		parent::redirect($destination, $args);
	}


	private function translateDestination(Product|LazyValue|Routable|string $destination, array $args): array
	{
		if ($destination instanceof LazyValue) {
			$destination = $destination->getEntity();
			if ($destination === null) {
				trigger_error('Bad CF LazyValue entity', E_USER_NOTICE);
				// value for common user without debug mode
				$destination = 'this';
			}
		}

		if (is_string($destination)) { // input: this, //this, logout!
			$mutation = $args['mutation'] ?? $this->mutation;
			if ($mutation->urlPrefix) {
				$args['urlPrefix'] = $mutation->urlPrefix;
			}
		}

		if ($destination instanceof Product) {
			$mutation = $args['mutation'] ?? $this->mutation;
			if ($mutation->urlPrefix) {
				$args['urlPrefix'] = $mutation->urlPrefix;
			}
			$destination = $destination->getLocalization($mutation);
		}

		return [$destination, $args];
	}



	public function formatTemplateFiles(): array
	{
		$fileName = static::getReflection()->getFileName();
		assert($fileName !== false);

		$dir = dirname($fileName);
		$dir = is_dir("$dir/templates") ? $dir : dirname($dir);
		return ["$dir/templates/$this->view.latte"];
	}



	public function createComponentFavouriteBase(): Favourite
	{
		$object = $this->getObjectForComponent($this->object);
		assert($object instanceof IEntity);
		return $this->favouriteFactory->create($object);
	}


	public function createComponentFavourite(): Multiplier
	{
		$object = $this->getObjectForComponent($this->object);

		return new Multiplier(function () use ($object): Favourite
		{
			assert($object instanceof IEntity);
			return $this->favouriteFactory->create($object);
		});
	}

	public function createComponentRating(): Rating
	{
		assert($this->object instanceof IEntity);
		return $this->ratingFactory->create($this->object);
	}

	private function getObjectForComponent(StaticPage|Routable $object): IEntity
	{
		if ($object instanceof StaticPage) {
			return $this->mutation->pages->title;
		} else {
			assert($object instanceof IEntity);
			return $object;
		}
	}


	private function getHrefLangs(): array
	{
		$hrefLangs = [];
//		return $hrefLangs;
		$mutations = $this->orm->mutation->findAll();
		if ($this->object instanceof \App\PostType\Page\Model\Orm\CommonTree && (bool)$this->object->uid) {
			/** @var Mutation $m */
			foreach ($mutations as $m) {
				if ((!(bool)$this->configService->get('mutations', $m->langCode))) {
					continue;
				}
				$tree = $this->orm->tree->getByUidAndRootId($this->object->uid, $m->rootId);
				if ($tree) {
					$hrefLangs[$m->langCode] = $m->getBaseUrlWithPrefix(). '/' . $tree->alias;
				}
			}
//		} elseif ($this->object instanceof \App\Model\Orm\ProductVariant\ProductVariant) {
//			foreach ($mutations as $m) {
//				if (!(bool)$this->configService->get('mutations', $m->langCode)) {
//					continue;
//				}
//				$loc = $this->object->getLocalization($m);
//				if ($loc) {
//					$hrefLangs[$m->langCode] = $m->getBaseUrlWithPrefix(). '/' . $loc->variant->>alias;
//				}
//			}
		} elseif ($this->object  instanceof RoutableEntity && $this->object instanceof LocalizationEntity) {
			$locs = $this->object->getParent()->getLocalizations();
			foreach ($locs as $loc) {
				assert($loc instanceof LocalizationEntity && $loc instanceof RoutableEntity);
				$hrefLangs[$loc->getMutation()->langCode] = $loc->getMutation()->getBaseUrlWithPrefix(). '/' . $loc->alias;
			}
		}

		// bd($hrefLangs);
		return $hrefLangs;
	}

}
