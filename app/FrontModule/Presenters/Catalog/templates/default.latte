{block content}

	<div class="row-main">
		<div class="content-indented u-pt-6 u-pt-8@md u-pt-9@lg">
			{snippet catalogHeader}
				{snippetArea breadcrumbArea}
					{control breadcrumb, [spacingBottom=>10, spacingBottomMd=>11, spacingBottomLg=>14]}
				{/snippetArea}
				{include $templates.'/part/box/annot.latte', seoLink=>$seoLink, spacingBottom=>12, spacingBottomMd=>15, spacingBottomLg=>24}
			{/snippet}
		</div>

{*		<div class="b-filter u-mb-last-0 u-mb-8 u-mb-9@md u-mb-22@lg">*}
{*			{snippet filterArea}*}
{*				{include 'part/filter.latte', class=>'b-filter__filter'}*}
{*			{/snippet}*}

{*			{snippet filterSetup}*}
{*				{include 'part/sort.latte', class=>'b-filter__sort'}*}
{*				{include 'part/selectedFilters.latte', class=>'b-filter__selected'}*}
{*			{/snippet}*}
{*		</div>*}

{*		{snippet products}*}
{*			{snippetArea productsInner}*}
{*				{include '../part/crossroad/folders.latte', folders=>$folders, productTitle=>'h2', ajaxPage=>true, dummyProductCarousel=>true}*}
{*			{/snippetArea}*}
{*		{/snippet}*}
	</div>
{/block}


{*control banner:shopTop*}

{*foreach $flashes as $flash}
	<div class="message message-{$flash->type}">{$flash->message}</div>
{/foreach*}


{*snippet catalogFooter}
	<div class="content-indented">
		{include '../part/box/content.latte'}
	</div>
{/snippet*}
