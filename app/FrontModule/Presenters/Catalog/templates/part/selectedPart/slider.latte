{varType App\Model\BucketFilter\Box\Slider $box}

<div class="b-filters__group">
	<p class="b-filters__title">
		{$box->title}:
	</p>

	<ul class="b-filters__list">

		{capture $link}{link 'this', filter => $box->filterToDeSelect}{/capture}
		{php $link = urldecode(htmlspecialchars_decode($link))}

		<li class="b-filters__item">
			<a href="{$link}" class="b-filters__remove" data-naja data-naja-loader="body"{if $linkSeo->hasNofollow($object, ['filter' => $box->filterToDeSelect])} rel="nofollow"{/if}>
				{if $box->inputValueMin != $box->selectedMin && $box->inputValueMax != $box->selectedMax}
					{$box->selectedMin}{if $box->unit} {$box->unit}{/if} - {$box->selectedMax}{if $box->unit} {$box->unit}{/if}
				{elseif $box->inputValueMin != $box->selectedMin}
					{_from} {$box->selectedMin}{if $box->unit} {$box->unit}{/if}
				{elseif $box->inputValueMax != $box->selectedMax}
					{_to} {$box->selectedMax}{if $box->unit} {$box->unit}{/if}
				{/if}
			</a>
		</li>
	</ul>
</div>
