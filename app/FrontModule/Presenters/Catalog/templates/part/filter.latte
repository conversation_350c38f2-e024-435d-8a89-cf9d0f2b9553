{default $class = 'u-mb-lg'}

{if isset($filter->boxes) && count($filter->boxes)}

	<form action="{link $object, filter=>[], 'selectedTab' => $selectedTab ?? null, 'pager-page' => null}" class="f-filter {$class}" data-controller="Filter" data-naja data-naja-history="off" data-naja-loader="body" method="get">

		{if isset($cleanFilterParam['search'])}
			<input type="hidden" name="search" value="{$cleanFilterParam['search']}">
		{/if}

		<p class="f-filter__toggle u-mb-6">
			<button type="button" class="select__btn inp-select inp-select--underline" data-action="Filter#toggle">
				{_btn_filter}
			</button>
		</p>


		{if isset($catalogOrder) && $catalogOrder != 'default'}
			<input name="order" type="hidden" value="{$catalogOrder}">
		{/if}

		<div class="f-filter__wrap" data-scrolll-lock-scrollable data-filter-target="wrap">
			{if isset($filter->boxes)}
				<div class="grid grid--y-0 grid--y-8@lg">
					{foreach $filter->boxes as $name=>$box}

						{if $box instanceOf App\Model\BucketFilter\Box\Slider}
							<div class="grid__cell size--2-12@lg">
								{include './boxes/slider.latte', box=>$box}
							</div>
						{else}
							<div class="grid__cell size--2-12@lg">
								{include './boxes/checkBoxes.latte', box=>$box}
							</div>
						{/if}
					{/foreach}
				</div>
			{/if}


			<div class="row-main f-filter__btns u-pt-5 u-mb-5 u-mb-12@md">
				<div class="grid grid--center grid--y-2">
					<div class="grid__cell size--auto">
						{capture $link}{link 'this', 'filter' => $filter->followingCleanFilterParameters, 'pager-page' => null}{/capture}
						{php $link = urldecode(htmlspecialchars_decode($link))}

						<a href="{$link}" class="btn btn--light">
						<span class="btn__text">
							{_"btn_filter_remove"}
						</span>
						</a>
					</div>
					<div class="grid__cell size--auto">
						<button type="submit" class="btn" name="filterSubmit">
						<span class="btn__text">
							{_"btn_show"}
						</span>
						</button>
					</div>
				</div>
			</div>



{*			{if isset($filter->nonRoot) && $filter->nonRoot}*}
{*				<p>*}
{*					{capture $link}{link 'this', 'filter' => $filter->followingCleanFilterParameters, 'pager-page' => null}{/capture}*}
{*					{php $link = urldecode(htmlspecialchars_decode($link))}*}

{*					<a href="{$link}" class="f-filter__remove">*}
{*						{_btn_filter_remove}*}
{*					</a>*}
{*				</p>*}
{*			{/if}*}

{*			<p> <!-- .u-js-hide -->*}
{*				<button type="submit" class="btn" name="filterSubmit">*}
{*					<span class="btn__text">*}
{*						{_btn_filter}*}
{*					</span>*}
{*				</button>*}
{*			</p>*}
		</div>
	</form>
{/if}

