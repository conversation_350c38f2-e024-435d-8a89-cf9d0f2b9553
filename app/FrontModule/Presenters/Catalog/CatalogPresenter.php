<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Catalog;

use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\BucketFilter\BucketFilterFactory;
use App\Model\BucketFilter\SetupCreator\Catalog;
use App\Model\BucketFilter\SortCreator;
use App\Model\Link\LinkSeo;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use stdClass;

/**
 * @method Tree getObject()
 * @property Tree $object
 */
class CatalogPresenter extends BasePresenter
{

	use HasCustomContentRenderer;

	private array $filterParams;

	private mixed $cleanFilterParam;

	public function __construct(
		private readonly BucketFilterFactory $bucketFilterFactory,
		private readonly SortCreator $sortCreator,
		private readonly Catalog\BasicElasticItemListFactory $basicElasticItemListFactory,
		private readonly Catalog\ElasticItemListFactory $elasticItemListFactory,
		private readonly Catalog\BoxListFactory $boxListFactory,
	)
	{
		parent::__construct();
	}


	protected function startup(): void
	{
		parent::startup();

		$this->template->seolink = false;

		$filterParams = $this->request->getParameter('filter');
		if ($filterParams === null) {
			$this->filterParams = [];
		} else {
			$this->filterParams = $filterParams;
		}

		$this->cleanFilterParam = $this->filterParams;
	}


	public function actionDefault(CatalogTree $object, array $filter, string $order = 'cheapest'): void
	{
		if ($object->hasLinkedCategories && $object->linkedCategories->count()) {
			$this->redirect($object->linkedCategories->fetch());
		}

		// exceptions in menu - redirect out
		if ($object->uid === 'eshop' && isset($object->cf->redirectURL->url)) {
			$this->redirectUrl($object->cf->redirectURL->url); //https://www.eshop.preciosalighting.com/en/homepage
		}

		$this->setObject($object);

		// je nasatveno presmerovani na jinou kategorii
		if (isset($this->object->cf->redirectToCategory->category) && $this->object->cf->redirectToCategory->category) {
			$this->redirect($this->object->cf->redirectToCategory->category);
		}
	}


	public function renderDefault(CatalogTree $object, string $order = 'cheapest', SeoLinkLocalization|null $seoLink = null): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $object;
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->get('shop', 'productsPaging');

		$this->template->cleanFilterParamForCrossroad = $this->cleanFilterParam;
		$this->template->cleanFilterParam = $this->cleanFilterParam;


		$basicElasticItemListGenerator = $this->basicElasticItemListFactory->create($object);
		$elasticItemListGenerator = $this->elasticItemListFactory->create($object, $this->currentState, $this->priceLevel, $this->filterParams);
		$boxListGenerator = $this->boxListFactory->create($object);

		$bucketFilter = $this->bucketFilterFactory->create(
			$basicElasticItemListGenerator,
			$elasticItemListGenerator,
			$boxListGenerator,
			$this->mutation,
		);
		$sort = $this->sortCreator->create($order, $this->currentState, $this->priceLevel);

		$filter = $bucketFilter->getFilter($this->filterParams);
		$itemsObject = $bucketFilter->getItems($paginator->itemsPerPage, $paginator->offset, $sort);



		$paginator->itemCount = $itemsObject->totalCount;

		$this->template->filter = $filter;
		$this->template->catalogOrder = $order;
		$this->template->categoriesProductCount = $filter->categories ?? [];
		$this->template->products = $itemsObject->items;
		$this->template->seoLink = $seoLink;
		$this->template->linkSeo = $this->linkSeo;

		if ($this->isAjax()) {
			if (isset($this->cleanFilterParam)) {
				$this->payload->newUrl = urldecode(htmlspecialchars_decode($this->link('//this', ['filter' => $this->cleanFilterParam])));
			}

			if (isset($this->params['more'])) {
				$this->redrawControl('productsInner');
				$this->redrawControl('productsPagerTop');
				$this->redrawControl('productsPagerBottom');
				$this->redrawControl('productList');
			} else {
				if (!$this->getSignal()) {
					$this->redrawControl();
				}
			}
		}
	}


	public function actionVisited(): void
	{
		$this->redirect($this->mutation->pages->eshop);
	}


	protected function beforeRender(): void
	{
		parent::beforeRender();

		$this->template->seolink = false;
		$this->template->isOpenInCookie = function ($name) {
			return $this->getHttpRequest()->getCookie('isOpen' . $name);
		};

		$this->addSeoFilterCatalog();
	}


	/**
	 * Prida do sablony SEO texty z kategorie, ktere se pouzijou pri filtorvani
	 */
	protected function addSeoFilterCatalog(): void
	{
		$seoFilterCatalog = new stdClass();
		$this->template->seoFilterCatalog = $seoFilterCatalog;
	}

}
