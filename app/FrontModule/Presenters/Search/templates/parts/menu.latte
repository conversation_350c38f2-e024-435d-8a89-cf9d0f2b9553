<nav class="b-sections__menu u-mb-14" data-controller="ResponsiveItems" data-sections-target="menu">
	<div class="b-sections__inner m-sections full-vw">
		<div class="row-main">
			<div class="m-sections__bottom" data-responsiveitems-target="wrap">
				{dump $tabs}
				<ul class="m-sections__list" n:inner-foreach="$tabs as $key => $collection">
					<li n:if="$collection && $collection->count()" class="m-sections__item" data-sections-target="item" data-responsiveitems-target="item">
						<a href="#{$key}" class="m-sections__link{*if $selectedTab == 'categories'} is-active{/if*}" data-controller="LinkSlide" data-action="LinkSlide#slideTo">
							{_$key}
							{if $key == 'others'}
								({$collection->count()+$trees->count()})
							{else}
								({$collection->count()})
							{/if}
						</a>
					</li>
				</ul>
				<div class="m-responsive u-hide" data-responsiveitems-target="collapsed" data-controller="Etarget">
					<a href="#" class="m-responsive__title" data-controller="ToggleClass" data-action="ToggleClass#toggle" data-toggle-class="is-open" data-toggle-class-content=".m-responsive">
						{_"more"}
					</a>
					<div class="m-responsive__popup">
						<ul class="m-responsive__list" n:inner-foreach="$tabs as $key => $collection">
							<li n:if="$collection && $collection->count()" class="m-responsive__item" data-Sections-target="responsiveItem" data-ResponsiveItems-target="collapsedItem">
								<a href="#{$key}" class="m-responsive__link" data-controller="LinkSlide" data-action="LinkSlide#slideTo">
									{_$key}
								</a>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
</nav>
