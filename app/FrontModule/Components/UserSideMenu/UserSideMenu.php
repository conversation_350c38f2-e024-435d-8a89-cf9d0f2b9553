<?php

declare(strict_types=1);

namespace App\FrontModule\Components\UserSideMenu;

use App\Model\Image\ImageObjectFactory;
use App\Model\MenuService;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\RoutableEntity;
use App\Model\Pages;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
final class UserSideMenu extends UI\Control
{
	private readonly Pages $pages;

	public function __construct(
		private readonly RoutableEntity $object,
		private readonly MenuService $menuService,
		private readonly ImageObjectFactory $imageObjectFactory,
		private readonly TranslatorDB $translator,
		MutationHolder $mutationHolder,
	)
	{
		$mutation = $mutationHolder->getMutation();
		$this->pages = $mutation->pages;
	}


	private function initTemplate(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->pages = $this->pages;
		$this->template->object = $this->object;
		$this->template->imageObjectFactory = $this->imageObjectFactory;
		$this->template->FE_TEMPLATE_DIR = FE_TEMPLATE_DIR;
	}


	public function render(): void
	{
		$this->initTemplate();
		$this->template->menu = isset($this->pages->userSection->cf->userSideMenu) ? $this->menuService->getUserMenu($this->object, true, $this->pages->userSection->cf->userSideMenu->fetchAll()) : null;
		$this->template->render(__DIR__ . '/userSideMenu.latte');
	}
}
