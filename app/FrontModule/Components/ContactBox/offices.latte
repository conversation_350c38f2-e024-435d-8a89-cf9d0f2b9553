<form n:name="locationForm" class="f-contacts u-bg-default full-vw u-mb-{$spacingBottom} u-mb-{$spacingBottomMd}@md u-mb-{$spacingBottomLg}@lg" data-naja data-naja-loader="body">
	<div class="row-main">
		<h2 n:if="$title" class="f-contacts__title h3 title title--center u-mb-11 u-mb-12@md u-mb-16@lg">
			{$title}
		</h2>

		<div class="u-mb-12 u-mb-14@md u-mb-19@lg">
				<select n:name="state" class="f-contacts__select inp-select inp-select--underline"
									   data-controller="Autosubmit"
									   data-action="Autosubmit#submitForm">
				</select>
				<p class="u-text-center u-js-hide u-mt-6">
					<button type="submit" class="btn">
					<span class="btn__text">
						{_"btn_change"}
					</span>
					</button>
				</p>
		</div>

		{if $offices->count()}
			{include $templates.'/part/crossroad/offices.latte', title=>false, items=>$offices, spacingBottom=>0, spaciingBottomMd=>0, spacingBottomLg=>0}
		{else}
			{_"contact_list_empty"}
		{/if}
	</div>
</form>
