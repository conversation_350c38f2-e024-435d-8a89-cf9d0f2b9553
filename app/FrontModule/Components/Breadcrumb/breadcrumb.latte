{default $spacingBottom = isset($props['spacingBottom']) ? $props['spacingBottom'] : '11'}
{default $spacingBottomMd = isset($props['spacingBottomMd']) ? $props['spacingBottomMd'] : '12'}
{default $spacingBottomLg = isset($props['spacingBottomLg']) ? $props['spacingBottomLg'] : '13'}

{if count($breadcrumbs) > 0}
	<nav aria-label="{_title_breadcrumb}" class="m-breadcrumb u-font-label u-mb-{$spacingBottom} u-mb-{$spacingBottomMd}@md u-mb-{$spacingBottomLg}@lg">
		<p class="m-breadcrumb__wrap">
			<strong class="u-vhide">
				{_title_breadcrumb}
			</strong>

			{foreach $breadcrumbs as $key=>$i}
				{if $i->public}
					<span class="m-breadcrumb__separator" n:if="!$iterator->first"></span>
					{if $iterator->last}
						{if $object instanceOf App\Model\ProductVariant}
							<a href="{plink $i}" class="m-breadcrumb__link">
								{$i->nameAnchor}
							</a>
							<span class="m-breadcrumb__separator"></span>
							<span class="m-breadcrumb__link">
								{$object->name}
							</span>
						{else}
							<span class="m-breadcrumb__link">
								{$i->nameAnchor}{if isset($_GET[search]) && $iterator->isLast() && $object->uid == 'search'}: {$_GET[search]}{/if}
							</span>
						{/if}
					{else}
						{if $iterator->first}
							<a href="{plink $i}" class="m-breadcrumb__link m-breadcrumb__link--home">
								{('house')|icon}
								<span class="u-vhide">
									{$i->nameAnchor}
								</span>
							</a>
						{else}
							<a href="{plink $i}" class="m-breadcrumb__link">
								{$i->nameAnchor}
							</a>
						{/if}
					{/if}
				{/if}
			{/foreach}
		</p>
	</nav>
{/if}
