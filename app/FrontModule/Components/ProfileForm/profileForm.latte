{snippet form}

	{form form autocomplete=>'off', novalidate=>"novalidate"}
		{control messageForForm, $flashes, $form}

		<h2>
			{_'title_personal_info'}
		</h2>
		{include '../inp.latte', form=>$form, name=>firstname}
		{include '../inp.latte', form=>$form, name=>lastname}
		{include '../inp.latte', form=>$form, name=>email}
		{include '../inp.latte', form=>$form, name=>phone, type=>'tel'}
		{include '../inp.latte', form=>$form, name=>street}
		{include '../inp.latte', form=>$form, name=>city}
		{include '../inp.latte', form=>$form, name=>zip}
		{include '../inp.latte', form=>$form, name=>state}

		<h2>
			{_'title_company_info'}
		</h2>
		{include '../inp.latte', form=>$form, name=>company}
		{include '../inp.latte', form=>$form, name=>ic}
		{include '../inp.latte', form=>$form, name=>dic}

		<h2>
			{_title_delivery_address}
		</h2>
		{if $customAddress}
			{foreach $customAddress as $k => $i}
				<div class="f-open" data-controller="ToggleCheckbox">
					<p>
						<label class="inp-item inp-item--checkbox f-open__inp">
							{var $caKey = 'ca_isRemove_'.$k}
							<input n:name="$caKey" class="inp-item__inp" data-action="change->ToggleCheckbox#changeClassInvert">
							<span class="inp-item__text">
								{_'form_label_remove_address'} {$form['ca_street_'.$k]->value}
							</span>
						</label>
					</p>
					<div class="f-open__box is-open" data-ToggleCheckbox-target="box">
						{include '../inp.latte', form=>$form, name=>'ca_firstname_'.$k, required=>true}
						{include '../inp.latte', form=>$form, name=>'ca_lastname_'.$k, required=>true}
						{include '../inp.latte', form=>$form, name=>'ca_company_'.$k, form=>$form}
						{include '../inp.latte', form=>$form, name=>'ca_phone_'.$k, type=>'tel'}
						{include '../inp.latte', form=>$form, name=>'ca_street_'.$k, required=>true}
						{include '../inp.latte', form=>$form, name=>'ca_city_'.$k, required=>true}
						{include '../inp.latte', form=>$form, name=>'ca_zip_'.$k, required=>true}
						{include '../inp.latte', form=>$form, name=>'ca_state_'.$k}
					</div>
				</div>
			{/foreach}
		{/if}

		<div class="f-open" data-controller="ToggleCheckbox">
			<p>
				<label class="inp-item inp-item--checkbox f-open__inp">
					<input n:name="ca_isNew" class="inp-item__inp" data-action="change->ToggleCheckbox#changeClass">
					<span class="inp-item__text">
						{_'form_label_add_address'}
					</span>
				</label>
			</p>
			<div n:class="f-open__box, $form['ca_isNew']->value ? is-open" data-ToggleCheckbox-target="box">
				{include '../inp.latte', form=>$form, name=>ca_firstname, required=>true}
				{include '../inp.latte', form=>$form, name=>ca_lastname,  required=>true}
				{include '../inp.latte', form=>$form, name=>ca_company, form=>$form}
				{include '../inp.latte', form=>$form, name=>ca_phone, type=>'tel'}
				{include '../inp.latte', form=>$form, name=>ca_street, required=>true}
				{include '../inp.latte', form=>$form, name=>ca_city, required=>true}
				{include '../inp.latte', form=>$form, name=>ca_zip, required=>true}
				{include '../inp.latte', form=>$form, name=>ca_state}
			</div>
		</div>

		{include '../inp.latte', form=>$form, name=>isNewsletter}

		<p>
			<button type="submit" class="btn" name="savePersonal">
				<span class="btn__text">
					{_'btn_save'}
				</span>
			</button>
		</p>

	{/form}


{/snippet}
