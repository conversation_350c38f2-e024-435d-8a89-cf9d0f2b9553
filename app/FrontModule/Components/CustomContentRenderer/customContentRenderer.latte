{snippet customContent}


	{default $props = []}
	{default $showTopMenu = $object->template == 'Reference:detail' || $object->template == 'Product:detail'}


	{if !isset($props['templateDirectory'])}
		{php $props['templateDirectory'] = $defaultTemplateDirectory}
	{/if}

	{if !isset($props['customContent'])}
		{php $props['customContent'] = $defaultObjectCC}
	{/if}

	{*{var $props = [*}
	{*	templateDirectory: (isset($props['templateDirectory'])) ? $props['templateDirectory'] : $defaultTemplateDirectory,*}
	{*	customContent: (isset($props['customContent'])) ? $props['customContent'] : $defaultObjectCC,*}
	{*]}*}

	{if !Nette\Utils\Strings::endsWith($props['templateDirectory'], '/')}
		{php $props['templateDirectory'] = $props['templateDirectory'].'/'}
	{/if}

	{var $hasAnchors = false}


	{foreach $props['customContent'] as $key=>$item}
		{if isset($item[0]->fixedMenuName)}
			{var $hasAnchors = true}
		{/if}
	{/foreach}



	{capture $ccComponents}
		{var $sameTemplateCounter = 0}
		{var $counter = 0}
		{var $prevItemTemplate = ''}

		{foreach $props['customContent'] as $key=>$item}
			{var $item = $item[0]} {*remove first level of group*}
			{var $templateName = substr($key, 0, strpos($key, '____'))}

			{if $prevItemTemplate !== $templateName}
				{php $sameTemplateCounter = 0}
				{php $prevItemTemplate = $templateName}
			{else}
				{php $sameTemplateCounter++}
			{/if}

			{if $isCheatsheet}
				{var $niceName = ($allCustomComponentsLabels[$templateName] ?? null) ?? $templateName}

				<div class="component u-mb-last-0">
				<strong class="component__title u-font-label">
					{$niceName}
				</strong>
			{/if}

{*					{dump $props['templateDirectory'].$templateName.'.latte'}*}
		{if $hasAnchors && isset($item->fixedMenuName)}<div id="{$key}" class="b-sections__item">{/if}

			{if $isDev}
				{include $props['templateDirectory'].$templateName.'.latte', props=>$props,customContentItem=>$item, parentIterator=>$iterator, sameTemplateCounter=>$sameTemplateCounter, counter=>$counter, ccKey=>$key}
			{else}
				{try}
					{include $props['templateDirectory'].$templateName.'.latte', props=>$props, customContentItem=>$item, parentIterator=>$iterator, sameTemplateCounter=>$sameTemplateCounter, counter=>$counter, ccKey=>$key}
				{/try}
			{/if}
		{if $hasAnchors && isset($item->fixedMenuName)}</div>{/if}

			{if $isCheatsheet}
				</div>
			{/if}
			{php $counter++}
		{/foreach}
	{/capture}

	{if $hasAnchors || $showTopMenu}
		<div id="sections" class="b-sections" data-controller="Sections">
			{php $introFiles = isset($object->cf->intro_files) ? $object->cf->intro_files->file : false}
		{include $templates.'/part/menu/sections.latte', menu=>$props['customContent'], top=>$showTopMenu, files=>$introFiles, hasAnchors=>$hasAnchors}
			<div class="b-sections__items u-clearfix">
				{$ccComponents}
			</div>
		</div>
	{else}
		{$ccComponents}
	{/if}

{/snippet}
