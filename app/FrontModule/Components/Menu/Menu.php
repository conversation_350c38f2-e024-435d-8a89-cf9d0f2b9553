<?php declare(strict_types=1);

namespace App\FrontModule\Components\Menu;



use App\FrontModule\Components\ToggleLanguage\ToggleLanguage;
use App\FrontModule\Components\ToggleLanguage\ToggleLanguageFactory;
use App\Model\ConfigService;
use App\Model\Image\ImageObjectFactory;
use App\Model\MenuService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Routable;
use App\Model\TranslatorDB;
use App\Model\ValidLazyValueDataExtractor;
use App\PostType\Core\Model\LocalizationEntity;
use Nette\Application\UI\Control;
use Nextras\Orm\Entity\IEntity;

class Menu extends Control
{

	public function __construct(
		private readonly IEntity $object,
		private readonly Mutation $mutation,
		private readonly TranslatorDB $translator,
		private readonly ConfigService $configService,
		private readonly MenuService $menuService,
		private readonly ImageObjectFactory $imageObjectFactory,
		private readonly ToggleLanguageFactory $toggleLanguageFactory,
		private readonly ValidLazyValueDataExtractor $validLazyValueDataExtractor
	)
	{}

	private function initTemplate(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->pages = $this->mutation->pages;
		$this->template->validLazyValueDataExtractor = $this->validLazyValueDataExtractor;
		$this->template->object = $this->object;
		$this->template->imageObjectFactory = $this->imageObjectFactory;
		$this->template->FE_TEMPLATE_DIR = FE_TEMPLATE_DIR;
	}


	public function createComponentToggleLanguage(): ToggleLanguage
	{
		assert( $this->object instanceof LocalizationEntity);
		return $this->toggleLanguageFactory->create($this->object);
	}

	public function render(): void
	{
		$this->initTemplate();
		$markSelected = true;
		assert($this->object instanceof Routable);
		$this->template->menu = $this->menuService->getMenu($this->mutation, $this->mutation->rootId, $this->object, $markSelected, $this->configService->getParam('mainMenu'));
		$this->template->mutation = $this->mutation;
		$this->template->render(__DIR__ . '/mainMenu.latte');
	}
}


