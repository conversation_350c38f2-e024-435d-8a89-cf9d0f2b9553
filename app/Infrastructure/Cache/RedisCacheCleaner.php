<?php declare(strict_types = 1);

namespace App\Infrastructure\Cache;

use Contributte\Console\Extra\Cache\Cleaners\ICleaner;
use Predis\ClientInterface;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use function assert;
use function file_get_contents;
use function sprintf;

final class RedisCacheCleaner implements ICleaner
{

	public function __construct(
		private readonly string $commonPrefix,
		private readonly string $currentPrefix,
		private readonly ClientInterface|null $redis = null,
	)
	{
	}

	public function getDescription(): string
	{
		return 'Redis cache storage';
	}

	public function clean(InputInterface $input, OutputInterface $output): bool
	{
		if ($this->redis === null) {
			return true;
		}

		$script = file_get_contents(__DIR__ . '/redis.clearCache.lua');
		assert($script !== false);

		$this->redis->eval($script, 0, $this->commonPrefix, $this->currentPrefix);
		$output->writeln(sprintf('<info>Successfully cleaned keys with old prefixes from redis database.</info>'));

		return true;
	}

}
