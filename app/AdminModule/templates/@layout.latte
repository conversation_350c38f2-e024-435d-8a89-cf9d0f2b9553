<!DOCTYPE html>
<!--[if IE 7 ]>    <html lang="cs" class="ie7 no-js"> <![endif]-->
<!--[if IE 8 ]>    <html lang="cs" class="ie8 no-js"> <![endif]-->
<!--[if IE 9 ]>    <html lang="cs" class="ie9 no-js"> <![endif]-->
<!--[if (gt IE 9)|!(IE)]><!--> <html lang="cs" class="no-js"> <!--<![endif]-->
	<head>
		<meta charset="utf-8" />
		<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
		<meta name="author" content="HTML by SuperKodéři (<EMAIL>)" />
		<meta name="keywords" content="" />
		<meta name="description" content="" />
		<meta name="robots" content="noindex, nofollow">
		<!--meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0" /-->
		<title>{$title}</title>
		<link href="https://fonts.googleapis.com/css?family=Open+Sans:400,400italic,700&subset=latin,latin-ext" rel="stylesheet" type="text/css" />
		<link rel="stylesheet" href="/admin/new/dist/css/style-old.css?v={$webVersion}" />
		<link rel="stylesheet" href="/admin/css/style.css?v={$webVersion}" media="screen, projection" />
		<link rel="stylesheet" href="/admin/css/developers.css?v={$webVersion}" media="screen, projection" />
		<link rel="stylesheet" href="/admin/css/print.css?v={$webVersion}" media="print" />

		<link rel="shortcut icon" href="/admin/favicon.ico?v={$webVersion}" />
		<script>document.documentElement.className = document.documentElement.className.replace('no-js', 'js');</script>
	</head>
	<body>
		<div id="header">
			<p id="logo">
				<span class="icon">
					<svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 80 100">
					<path d="M3.11,44.29l75.28,25.6c0.94,0.32,1.57,1.2,1.57,2.2c0,0.99-0.63,1.88-1.57,2.2L3.11,99.88c-1.21,0.41-2.53-0.24-2.94-1.45
						c-0.41-1.21,0.24-2.53,1.45-2.94l68.82-23.4L1.61,48.68c-1.21-0.41-1.86-1.73-1.45-2.94C0.57,44.53,1.89,43.88,3.11,44.29
						L3.11,44.29z M78.39,4.52L9.56,27.92l68.82,23.4c1.21,0.41,1.86,1.73,1.45,2.94c-0.41,1.21-1.73,1.86-2.94,1.45L1.61,30.11
						c-0.94-0.32-1.57-1.2-1.57-2.2c0-0.99,0.63-1.88,1.57-2.2l75.28-25.6c1.21-0.41,2.53,0.24,2.94,1.45C80.25,2.79,79.6,4.1,78.39,4.52
						L78.39,4.52z" fill="#3cdbc0"/>
					</svg>
				</span>{$title} {if isset($envName) && $envName}({$envName}){if $envName == "dev"} [starý]{/if}{/if}
			</p>
			<p id="user">
				<span class="icon icon-user"></span><a n:href=":Admin:User:edit $userEntity->id">{$userEntity->name}</a>  ({$userEntity->role})
				<a n:href=":Admin:Sign:out" class="logout"><span class="icon icon-switch"></span><span class="vhide">logout</span></a>
			</p>

			<div id="search">
				<span class="inp-fix inp-fix-suggest">
					<input class="inp-text inp-suggest-global w-full" name="q" data-suggest="/{$config['adminAlias']}/search/suggest-global" autocomplete="off" />
				</span>
				<div class="search-trigger">
					<span class="icon icon-search"></span>
				</div>
			</div>
		</div>
		<div id="main">

			<div class="col-side">

				{foreach $menu as $section => $items}
					<div class="menu-main">
						<h2 n:if="$section && !is_numeric($section)">{_$section}</h2>
						<ul class="reset" n:inner-foreach="$items as $i">
							{continueIf isset($i['devOnly']) && $i['devOnly'] === true && $userEntity->role !== 'developer'}
							<li n:class="isset($i['devOnly']) && $i['devOnly'] === true ? dev">
								{if $i['action'] == "Import:default"}
									{if $userEntity->role == "developer" || ($userEntity->role == "admin" && $userEntity->email=="<EMAIL>")}
										<a n:href="$i['action']" n:class="($presenter->name == $i['resource']) ? active, isset($i['sub']) ? sub"><span class="icon icon-{$i['icon']}"></span> <span class="name">{_$i['title']}</span></a>
									{/if}
								{else}
									<a n:href="$i['action']" n:class="($presenter->name == $i['resource']) ? active, isset($i['sub']) ? sub">
										<span class="icon icon-{$i['icon']}"></span>
										<span class="name">{_$i['title']}
											{if $i['action'] == "Order:default" && $count = $newOrders->count()}
												[<span style="color:#ee9900;">{$count}</span>]
											{/if}
										</span>
									</a>
								{/if}
							</li>
						</ul>
					</div>
				{/foreach}
			</div>
			<div class="col-main">

				{*snippet flash}
					<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
				{/snippet*}

				{block #tree}{/block}

				{include #content}

			</div>

		</div>
		<script src="/admin/js/jquery.js"></script>
		<script src="/admin/js/jquery.plugin.js"></script>
		<script src="/admin/js/jquery-ui.js"></script>
		<script src="/admin/js/jquery.ui.datetimepicker.js"></script>
		<script src="/admin/js/jquery.nette.js"></script>
		<script src="/admin/js/jquery.cookie.js"></script>
		<script src="/admin/js/jquery.jstree.js"></script>
		<script src="/admin/js/uploadify5/jquery.uploadifive.min.js"></script>
		<script src="/admin/js/fancybox/jquery.mousewheel-3.0.4.pack.js"></script>
		 <!-- <script src="/admin/js/fancybox/jquery.fancybox-1.3.4.pack.js"></script> -->
		<script src="/admin/js/tinymce5/tinymce.min.js"></script>
		<script src="/admin/js/tinymce5/jquery.tinymce.min.js"></script>
		<script src="/admin/js/sk.js"></script>
		<script src="/admin/js/sk/sk.widgets.Suggest.js"></script>
		<script src="/admin/js/sk/sk.widgets.SuggestMenu.js"></script>
		<script src="/admin/js/app.js?v={$webVersion}"></script>
		<script src="/admin/new/dist/js/appCustomFields.js?v={$webVersion}"></script>

		<script>
			App.run({
				sessionID: {session_id()},
			});

			AppCf.run();
		</script>
	</body>
</html>
