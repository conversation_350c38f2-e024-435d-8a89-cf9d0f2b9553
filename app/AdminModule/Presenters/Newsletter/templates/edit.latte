{block #content}

{*{snippet editEmailForm}*}
	{form editEmailForm class => 'ajaax-form', autocomplete => 'off'}

		<h1><a n:href="Newsletter:emails">{_"Newsletter Emails"}</a> - {if isset($object->email)}{_"Edit email"}{else}{_"Create new email"}{/if}</h1>
		{input id}


		<ul class="message message-error" n:if="$form->hasErrors()">
			<li n:foreach="$form->errors as $error">{$error}</li>
		</ul>

		<p n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</p>
		<div class="grid-row">
			<p class="grid-1-2">
				{label mutation /}<br>
				<span class="inp-fix">
					{input mutation class => 'inp-text w-full'}
				</span>
			</p>
		</div><br>
		<div class="grid-row">
			<p class="grid-1-2">
				{label email /}<br>
				<span class="inp-fix">
					{input email class => 'inp-text w-full'}
				</span>
			</p>

		</div>

		{*<div class="grid-row">*}
			{*<p class="grid-1-2">*}
				{*{label firstname /}<br>*}
				{*<span class="inp-fix">*}
					{*{input firstname class => 'inp-text w-full'}*}
				{*</span>*}
			{*</p>*}
			{*<p class="grid-1-2">*}
				{*{label lastname /}<br>*}
				{*<span class="inp-fix">*}
					{*{input lastname class => 'inp-text w-full'}*}
				{*</span>*}
			{*</p>*}
		{*</div>*}

		<div class="fixed-bar">
			<button class="btn btn-green btn-icon-before">
				<span><span class="icon icon-checkmark"></span> {_save_button}</span>
			</button>
			{if isset($object->id) && $object->id != $userEntity->id}
			<a n:href="delete! $object->id" class="btn btn-red btn-icon-before btn-delete ajax">
				<span><span class="icon icon-close"></span> {_delete_button}</span>
			</a>
			{/if}
		</div>

	{/form}

{*{/snippet}*}
