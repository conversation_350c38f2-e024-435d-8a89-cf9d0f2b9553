{if isset($esResult)}
	<ul class="reset">
		{dump $esResult->getResults()}
	{foreach $esResult->getResults() as $k => $i}
		{varType Elastica\Result $i}
		{dump $i->kind}
		{if $i->kind == 'blog'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Blog:Admin:Blog:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'blogTag'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":BlogTag:Admin:BlogTag:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'author'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Author:Admin:Author:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'press'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Press:Admin:Press:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'reference'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Reference:Admin:Reference:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'event'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Event:Admin:Event:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'designer'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Designer:Admin:Designer:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'contact'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Contact:Admin:Contact:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'person'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Person:Admin:Person:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'tree'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Page:Admin:Page:default $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'seoLink'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Admin:SeoLink:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'product'}
			<li class="item" data-id="{$i->id}">
				[product]
				<a n:href=":Admin:Product:edit $i->id">{$i->name|prepareStrJs}</a>
			</li>
		{/if}
	{/foreach}
	</ul>
{/if}
