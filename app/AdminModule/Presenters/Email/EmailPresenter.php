<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Email;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Email\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\Email\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Email\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\Email\Components\ShellForm\ShellFormFactory;
use App\Model\Orm\EmailTemplate\EmailTemplate;
use App\Model\Orm\EmailTemplate\EmailTemplateModel;
use App\Model\Orm\File\FileModel;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Http\FileUpload;
use Nextras\Dbal\Drivers\Exception\UniqueConstraintViolationException;
use Throwable;
use Tracy\Debugger;
use <PERSON>\ILogger;

final class EmailPresenter extends BasePresenter
{

	protected ?EmailTemplate $object = null;

	public function __construct(
		private readonly EmailTemplateModel $emailTemplateModel,
		private readonly DataGridFactory $dataGridFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly FileModel $fileModel,

	)
	{
		parent::__construct();
	}


	public function actionEdit(int $id): void
	{
		$object = $this->orm->emailTemplate->getById($id);

		if ($object === null) {
			$this->redirect('default');
		}

		$this->object = $object;

		if ($this->object->isDeveloper && !$this->user->isDeveloper()) {
			$this->redirect('default');
		}
	}


	protected function createComponentEditForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);
		$form->addText('name', 'label_internalName')->setRequired();

		$form->addText('subject', 'label_subject');
		$form->addTextArea('body', 'label_emailContent', 50, 40);

		if ($this->user->isDeveloper()) {
			$form->addText('key', 'label_key')->setRequired();
			$form->addCheckbox('isDeveloper', 'onlyForDevelopers');
		}

//		$form->addSelect('mutation', 'label_mutation', $this->orm->mutation->findAll()->fetchPairs('id', 'name'));

		if ($this->object !== null) {
			$form['name']->setDefaultValue($this->object->name);
			$form['subject']->setDefaultValue($this->object->subject);
			$form['body']->setDefaultValue($this->object->body);
//			$form['mutation']->setDefaultValue($this->object->mutation->id);


			if ($this->user->isDeveloper()) {
				$form['key']->setDefaultValue($this->object->key);
				$form['isDeveloper']->setDefaultValue($this->object->isDeveloper);
			}
		}

		$form->addSubmit('save', 'Save');

		$form->onSuccess[] = [$this, 'editFormSucceeded'];
		$form->onError[] = [$this, 'editFormError'];
		return $form;
	}

	public function editFormError(UI\Form $form): void
	{
		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}

	public function editFormSucceeded(UI\Form $form): void
	{
		try {
			$valuesAll = $form->getHttpData();
			$emailTemplate = $this->emailTemplateModel->save($this->object, $valuesAll);

			$this->flashMessage('done', 'ok');
			if ($this->object) {
				if (isset($valuesAll['saveBack'])) {
					$this->redirect('Email:default');
				}

				if ($this->isAjax()) {
					$this->redrawControl();
				} else {
					$this->redirect('this');
				}
			} else {
				$this->redirect('edit', ['id' => $emailTemplate->id]);
			}
		} catch (UniqueConstraintViolationException $e) {
			$form->addError('msg_template_key_exist');
			$this->flashMessage('msg_template_key_exist', 'error');

		} catch (AbortException $e) {
			throw $e;

		} catch (Throwable $e) {
			Debugger::log($e, ILogger::ERROR);
			$form->addError('msg_operation_failed');
			$this->flashMessage('msg_operation_failed', 'error');
		}
	}


	public function handleDelete(int $id): void
	{
		$emailTemplate = $this->orm->emailTemplate->getById($id);

		if ($emailTemplate) {
			$this->emailTemplateModel->delete($emailTemplate);
			$this->flashMessage('done', 'ok');
		} else {
			$this->flashMessage('Unknown template', 'error');
		}

		$this->redirect('default');
	}

	public function handleUploadFiles(?FileUpload $file = null): never
	{
		$file ??= new FileUpload($_FILES['Filedata']);

		$fileEntity = $this->fileModel->add($file);
		$template = $this->createTemplate()->setFile(__DIR__ . '/templates/part/form/files.latte');


		$template->data = $fileEntity;
		$template->render();
		$this->terminate();
	}



	protected function createComponentGrid(): DataGrid
	{

		return $this->dataGridFactory->create($this->user->isDeveloper());
	}

	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

}
