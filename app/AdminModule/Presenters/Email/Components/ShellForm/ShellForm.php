<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Email\Components\ShellForm;

use App\AdminModule\Presenters\Elastic\Components\ShellForm\FormData\BaseFormData;
use App\Model\Orm\EmailTemplate\EmailTemplateModel;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nextras\Orm\Collection\ICollection;

class ShellForm extends Control
{

	private ICollection $mutations;

	public function __construct(
		private readonly Translator $translator,
		private readonly EmailTemplateModel $emailTemplateModel,
		private readonly MutationRepository $mutationRepository,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$this->mutations = $this->mutationRepository->findBy([]);
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('mutations', $this->mutations);

		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);

		$form->addSelect('mutation', 'select_mutation', $this->mutations->fetchPairs('id', 'name'))->setRequired();
		$form->addSubmit('send', 'send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}



	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, BaseFormData $data): void
	{
		$mutation = $this->mutationRepository->getByIdChecked($data->mutation);
		$emailTemplate = $this->emailTemplateModel->create($mutation);
		$this->presenter->redirect('edit', ['id' => $emailTemplate->id]);
	}

}
