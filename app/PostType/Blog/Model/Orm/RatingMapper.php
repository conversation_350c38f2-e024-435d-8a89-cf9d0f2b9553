<?php declare(strict_types = 1);

namespace App\PostType\Blog\Model\Orm;


use App\Model\Orm\Orm;
use App\Model\Orm\Traits\HasCamelCase;
use Nette\Caching\Cache;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\IConnection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;
use Nextras\Orm\Mapper\Dbal\DbalMapperCoordinator;

class RatingMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'rating';

	public function __construct(

		IConnection $connection,
		DbalMapperCoordinator $mapperCoordinator,
		Cache $cache,
	)
	{
		parent::__construct($connection, $mapperCoordinator, $cache);
	}


	public function getRatings(): ArrayHash
	{
		$sql = "SELECT *, Count(*) FROM rating GROUP BY `module`, `moduleItemId`";
		$res = $this->connection->query($sql)->fetchAll();

		$arrayResult = [];
		foreach ($res as $r) {
			$tmpR = $r->toArray();

			$rating = $this->getRating($tmpR['module'], $tmpR['moduleItemId']);
			if (isset($rating->rating)) {
				$rating->percentage = round($rating->rating/5*100);
			} else {
				$rating->percentage = 0;
				$rating->rating = 0;
			}

			$rating->module = $tmpR['module'];
			$rating->moduleItemId = $tmpR['moduleItemId'];

			$arrayResult[] = $rating;
		}

		return ArrayHash::from($arrayResult);

	}


	public function getRating(string $module, int $moduleItemId): ArrayHash
	{
		$sql = "SELECT AVG(vote) rating, COUNT(*) count FROM rating WHERE `module` = %s AND `moduleItemId` = %i";
		$res = $this->connection->query($sql, $module, $moduleItemId)->fetch();
		if (isset($res)) {
			return ArrayHash::from($res->toArray());
		} else {
			return new ArrayHash();
		}
	}

}
