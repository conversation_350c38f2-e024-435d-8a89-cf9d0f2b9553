<?php declare(strict_types = 1);

namespace App\PostType\Press\AdminModule\Components\Form;

use App\PostType\Press\Model\Orm\PressLocalization;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\PostType\Press\AdminModule\Components\Form\FormData\BaseFormData;
use Nette\Utils\ArrayHash;

final class Handler
{
	public function __construct(
		private readonly Orm $orm,
		private readonly \App\PostType\Core\AdminModule\Components\Form\Handler $coreHandler,
	)
	{
	}


	public function handle(PressLocalization $pressLocalization, User $user, BaseFormData $data, ArrayHash $postData): void
	{
		$pressLocalization->isMain = (int) $data->isMain;

		$this->coreHandler->handleLocalization($pressLocalization, $data->localization);
		$this->coreHandler->handleParent($pressLocalization->getParent(), $data->parent);
		$this->coreHandler->handleValidity($pressLocalization, $data->validity);
		$this->coreHandler->handlePublish($pressLocalization, $data->publish);
		$this->coreHandler->handleRoutable($pressLocalization, $data->localization, $data->routable, $pressLocalization->getMutation());
		$this->coreHandler->handleEditor($pressLocalization, $user);

		$this->coreHandler->handleRelations($this->orm->tree, $pressLocalization, $postData, 'categories');

		$this->orm->pressLocalization->persistAndFlush($pressLocalization);
	}

}
