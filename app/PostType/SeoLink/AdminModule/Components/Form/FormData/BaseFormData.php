<?php

declare(strict_types=1);

namespace App\PostType\SeoLink\AdminModule\Components\Form\FormData;

use App\PostType\Core\AdminModule\Components\Form\FormData\LocalizationFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\ParentFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\PublishFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\RoutableFormData;

final class BaseFormData
{

	public ParentFormData $parent;

	public RoutableFormData $routable;

	public LocalizationFormData $localization;

	public PublishFormData $publish;

}
