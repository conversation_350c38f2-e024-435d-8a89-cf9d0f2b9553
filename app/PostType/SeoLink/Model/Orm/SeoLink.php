<?php

declare(strict_types=1);

namespace App\PostType\SeoLink\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\JsonArrayHashContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use function implode;
use function sort;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property ArrayHash $customFieldsJson {container JsonArrayHashContainer}
 * @property string|null $parameterValuesIds
 * @property string $type {enum self::TYPE_*} {default self::TYPE_PRODUCT}
 *
 * RELATIONS
 * @property SeoLinkLocalization[]|OneHasMany $localizations {1:m SeoLinkLocalization::$seoLink}
 * @property ParameterValue[]|ManyHasMany $parameterValues {m:m ParameterValue, isMain=true, oneSided=true}
 *
 * VIRTUAL
 * @property ArrayHash $cf {virtual}
 */
class SeoLink extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	const TYPE_PRODUCT = 'product';
	const TYPE_REFERENCE = 'reference';

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}


	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}


	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): SeoLinkLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof SeoLinkLocalization);
		return $localization;
	}

	public function onBeforePersist(): void
	{
		$parameterValuesIds = $this->parameterValues->toCollection()->fetchPairs(value: 'id');
		sort($parameterValuesIds);

		if ($parameterValuesIds === []) {
			$this->parameterValuesIds = null;
		} else {
			$this->parameterValuesIds = implode(',', $parameterValuesIds);
		}
	}

}
