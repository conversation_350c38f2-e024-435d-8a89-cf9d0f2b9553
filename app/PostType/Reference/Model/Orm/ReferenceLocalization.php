<?php declare(strict_types = 1);

namespace App\PostType\Reference\Model\Orm;

use App\Model\Orm\HasImages;
use App\Model\Orm\ImageEntity;
use App\Model\Orm\JsonContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Core\Model\Hidable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;

/**
 * @property int $id {primary}
 * @property string $name {default ''}
 * @property bool $public {default false}
 * @property bool $hide {default false}
 * @property DateTimeImmutable|null $publicFrom {default now}
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 * @property int $sort {default 0}
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property Reference $reference {M:1 Reference::$localizations}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read string $template {virtual}
 * @property-read  ReferenceLocalization[]|ICollection $attachedReferences {virtual}
 */
class ReferenceLocalization extends RoutableEntity implements LocalizationEntity, Publishable, Validatable, HasImages, Hidable
{

	use HasCustomFields;
	use HasCustomContent;

	private BlogLocalizationRepository $blogLocalizationRepository;


	public function injectBlogRepository(BlogLocalizationRepository $blogLocalizationRepository): void
	{
		$this->blogLocalizationRepository = $blogLocalizationRepository;
	}

	protected function getterTemplate(): string
	{
		return ':Reference:Front:Reference:detail';
	}


	protected function getterPath(): array
	{
		$blogPage = $this->mutation->pages->references;
		$path = $blogPage->path;
		$path[] = $blogPage->id;
		return $path;
	}



	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): Reference
	{
		return $this->reference;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof Reference);
		$this->reference = $parentEntity;
	}


	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getPublicFrom(): DateTimeImmutable|null
	{
		return $this->publicFrom;
	}

	public function getPublicTo(): DateTimeImmutable|null
	{
		return $this->publicTo;
	}

	public function setPublicFrom(?DateTimeImmutable $publicFrom): void
	{
		$this->publicFrom = $publicFrom;
	}

	public function setPublicTo(?DateTimeImmutable $publicTo): void
	{
		$this->publicTo = $publicTo;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getFirstImage(): ImageEntity|null
	{
		return isset($this->getParent()->cf->base->mainImage) ? $this->getParent()->cf->base->mainImage->getEntity() : null;
	}

	protected function getterAttachedReferences(): ICollection
	{
		$repository = $this->getRepository();
		assert($repository instanceof ReferenceLocalizationRepository);

		$attachedReferenceIds = $this->reference->attachedReferences->toCollection()->fetchPairs(null, 'id');
		return $repository
			->findBy([
				'reference->id' => $attachedReferenceIds,
				'mutation' => $this->mutation,
			]);
	}
	
	public function getHide(): bool
	{
		return $this->hide;
	}

	public function setHide(bool $hide): void
	{
		$this->hide = $hide;
	}
}
