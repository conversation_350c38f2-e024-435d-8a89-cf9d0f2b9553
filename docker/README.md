# Local Docker development

## Infrastructure setup

All Superadmin-based projects use a common infrastructure set up by the docker-compose stack in [docker-superadmin](https://bitbucket.org/superkoders/docker-superadmin) repository. This allows you to run multiple Superadmin-based projects alongside each other.

**☝ Please make sure to clone the repository and follow the instructions therein.**


## Quick start

### Application configuration

To quickly set your local instance up for the docker-compose installation, modify your `app/config/config.local.neon` to include `environment.docker.neon` instead of `environment.dev.neon`:

```neon
includes:
	- environment.docker.neon
```

This file configures the application to access the docker-compose containers that run the database, Elasticsearch index, Redis cache and possibly other services. Therefore, you can delete or comment out any local configuration of the database, ES, Redis, etc. You can keep any additional configuration that you need for local development.

### Set up the services

Run a sequence of setup tasks:

1. Install all necessary dependencies:

    ```shell
    make install
    ```

2. Run a one-time build of client-side assets so that they are available even if the development containers are not running:

    ```shell
    make build
    ```

3. Create all the database tables, and populate them with necessary basic data, and also dummy data for testing:

    ```shell
    make migrate
    ```

4. Create Elasticsearch indices, populate them with data from the database, and automatically set them up for immediate use:

    ```shell
    make populate-elastic
    ```

### Launch the Docker stack

```shell
make up
```

This should build and boot up all necessary containers, and the application should now be up and running and available via the URL `https://superadmin.superkoders.test`.


## Commands

### Available shortcuts

The Makefile defines a few more useful commands:

- `make up` starts the whole stack.
- `make stop` stops all running containers.
- `make install-php`, `make install-front`, `make install-admin`, and `make install-admin-new` install respective dependencies, and `make install` installs all of them.
- `make build-front`, `make build-admin`, `make build-admin-new` run a one-time build of respective client-side assets, and `make build` runs all of them.
- `make migrate` and `make migrations-reset` update or reset your database schema.
- `make populate-elastic` populates your Elasticsearch index with data from the database.

### Running other commands

You can also run pretty much any command directly via `docker-compose`: just specify the correct service and command:

```shell
docker-compose exec -it <service> <command>
```

The services are:

- `app`, a PHP+Apache container running the PHP application;
- `front`, a Node container running the front development build;
- `admin`, a Node container running the new admin development build.

#### Example: adding a Composer dependency

```shell
docker-compose exec -it app php composer.phar require new/dependency
```

#### Example: running a PHP console command

```shell
docker-compose exec -it app php bin/console command:to:run
```

#### Example: adding an NPM dependency to front

```shell
docker-compose exec -it front npm i @new/dependency
```

#### Example: adding an NPM dependency to new admin

```shell
docker-compose exec -it admin npm i @new/dependency
```

#### Example: launching an interactive shell in a container

```shell
docker-compose exec -it app bash
```

## Debugging PHP

The `app` container by default runs without Xdebug. To enable it, you need to rerun the container with the right configuration. There are two shorthand commands defined in the Makefile that toggle Xdebug in the application container: `make xdebug-on` and `make xdebug-off`.

It should be configured to connect to your local machine (the Docker host) for remote debugging. You only need to configure the path mappings in PhpStorm so that it can map the paths within the container to your local repository paths:

1. Open PhpStorm Settings.
2. Go to PHP > Servers.
3. Add or update a Server with:
   - Host: superadmin.superkoders.test
   - Port: 80
   - Debugger: Xdebug
4. Check "Use path mappings".
5. Set the "Absolute path on the server" associated with the repository's root directory to `/var/www/html`.
