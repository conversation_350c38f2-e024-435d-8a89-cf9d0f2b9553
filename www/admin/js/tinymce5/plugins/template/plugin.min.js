/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.0.1 (2019-02-21)
 */
!function(){"use strict";var e,t,n,r,a,o=tinymce.util.Tools.resolve("tinymce.PluginManager"),u=function(e){return function(){return e}},i=u(!1),c=u(!0),f=tinymce.util.Tools.resolve("tinymce.util.Tools"),p=tinymce.util.Tools.resolve("tinymce.util.XHR"),l=function(e){return e.getParam("template_cdate_classes","cdate")},s=function(e){return e.getParam("template_mdate_classes","mdate")},m=function(e){return e.getParam("template_selected_content_classes","selcontent")},d=function(e){return e.getParam("template_preview_replace_values")},g=function(e){return e.getParam("template_replace_values")},y=function(e){return e.templates},v=function(e){return e.getParam("template_cdate_format",e.translate("%Y-%m-%d"))},h=function(e){return e.getParam("template_mdate_format",e.translate("%Y-%m-%d"))},b=function(e,t){if((e=""+e).length<t)for(var n=0;n<t-e.length;n++)e="0"+e;return e},T=function(e,t,n){var r="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),a="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),o="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),u="January February March April May June July August September October November December".split(" ");return n=n||new Date,t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace("%D","%m/%d/%Y")).replace("%r","%I:%M:%S %p")).replace("%Y",""+n.getFullYear())).replace("%y",""+n.getYear())).replace("%m",b(n.getMonth()+1,2))).replace("%d",b(n.getDate(),2))).replace("%H",""+b(n.getHours(),2))).replace("%M",""+b(n.getMinutes(),2))).replace("%S",""+b(n.getSeconds(),2))).replace("%I",""+((n.getHours()+11)%12+1))).replace("%p",n.getHours()<12?"AM":"PM")).replace("%B",""+e.translate(u[n.getMonth()]))).replace("%b",""+e.translate(o[n.getMonth()]))).replace("%A",""+e.translate(a[n.getDay()]))).replace("%a",""+e.translate(r[n.getDay()]))).replace("%%","%")},M=function(n,e){return f.each(e,function(e,t){"function"==typeof e&&(e=e(t)),n=n.replace(new RegExp("\\{\\$"+t+"\\}","g"),e)}),n},_=function(e,t){var r=e.dom,a=g(e);f.each(r.select("*",t),function(n){f.each(a,function(e,t){r.hasClass(n,t)&&"function"==typeof a[t]&&a[t](n)})})},O=function(e,t){return new RegExp("\\b"+t+"\\b","g").test(e.className)},S=function(t,n){return function(){var e=y(t);"function"!=typeof e?"string"==typeof e?p.send({url:e,success:function(e){n(JSON.parse(e))}}):n(e):e(n)}},x=M,P=_,A=function(t,e,n){var r,a,o=t.dom,u=t.selection.getContent();n=M(n,g(t)),r=o.create("div",null,n),(a=o.select(".mceTmpl",r))&&0<a.length&&(r=o.create("div",null)).appendChild(a[0].cloneNode(!0)),f.each(o.select("*",r),function(e){O(e,l(t).replace(/\s+/g,"|"))&&(e.innerHTML=T(t,v(t))),O(e,s(t).replace(/\s+/g,"|"))&&(e.innerHTML=T(t,h(t))),O(e,m(t).replace(/\s+/g,"|"))&&(e.innerHTML=u)}),_(t,r),t.execCommand("mceInsertContent",!1,r.innerHTML),t.addVisual()},w=function(e){e.addCommand("mceInsertTemplate",function t(r){for(var a=[],e=1;e<arguments.length;e++)a[e-1]=arguments[e];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=a.concat(e);return r.apply(null,n)}}(A,e))},D=function(r){r.on("PreProcess",function(e){var t=r.dom,n=h(r);f.each(t.select("div",e.node),function(e){t.hasClass(e,"mceTmpl")&&(f.each(t.select("*",e),function(e){t.hasClass(e,r.getParam("template_mdate_classes","mdate").replace(/\s+/g,"|"))&&(e.innerHTML=T(r,n))}),P(r,e))})})},C=i,N=c,H=function(){return k},k=(r={fold:function(e,t){return e()},is:C,isSome:C,isNone:N,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:n,orThunk:t,map:H,ap:H,each:function(){},bind:H,flatten:H,exists:C,forall:N,filter:H,equals:e=function(e){return e.isNone()},equals_:e,toArray:function(){return[]},toString:u("none()")},Object.freeze&&Object.freeze(r),r),I=function(n){var e=function(){return n},t=function(){return a},r=function(e){return e(n)},a={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:N,isNone:C,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return I(e(n))},ap:function(e){return e.fold(H,function(e){return I(e(n))})},each:function(e){e(n)},bind:r,flatten:e,exists:r,forall:r,filter:function(e){return e(n)?a:k},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(C,function(e){return t(n,e)})},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return a},J={some:I,none:H,from:function(e){return null===e||e===undefined?k:I(e)}},L=(a="function",function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(e)===a}),Y=(Array.prototype.slice,L(Array.from)&&Array.from,tinymce.util.Tools.resolve("tinymce.util.Promise")),F=function(t,e){if(-1===e.indexOf("<html>")){var n="";f.each(t.contentCSS,function(e){n+='<link type="text/css" rel="stylesheet" href="'+t.documentBaseURI.toAbsolute(e)+'">'});var r=t.settings.body_class||"";-1!==r.indexOf("=")&&(r=(r=t.getParam("body_class","","hash"))[t.id]||""),e="<!DOCTYPE html><html><head>"+n+'</head><body class="'+r+'">'+e+"</body></html>"}return x(e,d(t))},j=function(c,t){var e=function(e){return function(e,t){for(var n=e.length,r=new Array(n),a=0;a<n;a++){var o=e[a];r[a]=t(o,a,e)}return r}(e,function(e){return{text:e.text,value:e.text}})},l=function(e,t){return function(e,t){for(var n=0,r=e.length;n<r;n++){var a=e[n];if(t(a,n,e))return J.some(a)}return J.none()}(e,function(e){return e.text===t})},s=function(e){return new Y(function(t,n){e.value.url?p.send({url:e.value.url,success:function(e){t(e)},error:function(e){n(e)}}):t(e.value.content)})};(function(){if(t&&0!==t.length)return J.from(f.map(t,function(e,t){return{selected:0===t,text:e.title,value:{url:e.url,content:e.content,description:e.description}}}));var e=c.translate("No templates defined.");return c.notificationManager.open({text:e,type:"info"}),J.none()})().each(function(a){var o=e(a),u=function(e,t){return{title:"Insert Template",size:"large",body:{type:"panel",items:e},initialData:t,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],onSubmit:(n=a,function(t){var e=t.getData();l(n,e.template).each(function(e){s(e).then(function(e){A(c,!1,e),t.close()})})}),onChange:(r=a,function(n,e){if("template"===e.name){var t=n.getData().template;l(r,t).each(function(e){n.block("Loading..."),s(e).then(function(e){var t=F(c,e);n.setData({preview:t}),n.unblock()})})}})};var r,n},i=c.windowManager.open(u([],{template:"",preview:""}));i.block("Loading..."),s(a[0]).then(function(e){var t=F(c,e),n=[{type:"selectbox",name:"template",label:"Templates",items:o},{label:"Preview",type:"iframe",name:"preview",sandboxed:!1}],r={template:a[0].text,preview:t};i.unblock(),i.redial(u(n,r)),i.focus("template")})})},q=function(t){return function(e){j(t,e)}},E=function(e){e.ui.registry.addButton("template",{icon:"template",tooltip:"Insert template",onAction:S(e.settings,q(e))}),e.ui.registry.addMenuItem("template",{icon:"template",text:"Insert template...",onAction:S(e.settings,q(e))})};o.add("template",function(e){E(e),w(e),D(e)}),function R(){}}();