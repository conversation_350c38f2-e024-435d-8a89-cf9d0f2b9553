/* jsTree default theme */
@import "base.less";

@clicked-color: #e7f4f9;
@image-path: "";
.gradient(@color1; @color2) {
	background:@color1;
	background: -moz-linear-gradient(top, @color1 0%, @color2 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,@color1), color-stop(100%,@color2));
	background: -webkit-linear-gradient(top, @color1 0%,@color2 100%);
	background: -o-linear-gradient(top, @color1 0%,@color2 100%);
	background: -ms-linear-gradient(top, @color1 0%,@color2 100%);
	background: linear-gradient(to bottom, @color1 0%,@color2 100%);
	/*filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='@color1', endColorstr='@color2',GradientType=0 );*/
}
.jstree-theme (@base-height, @image, @image-height) {
	@correction: (@image-height - @base-height) / 2;
	
	.jstree-node { min-height:@base-height; line-height:@base-height; margin-left:@base-height; min-width:@base-height; }
	.jstree-anchor { line-height:@base-height; height:@base-height; }
	.jstree-icon { width:@base-height; height:@base-height; line-height:@base-height; }
	.jstree-icon:empty { width:@base-height; height:@base-height; line-height:@base-height; }
	&.jstree-rtl .jstree-node { margin-right:@base-height; }
	.jstree-wholerow { height:@base-height; }

	.jstree-node, 
	.jstree-icon { background-image:url("@{image}"); }
	.jstree-node { background-position:-(@image-height * 9 + @correction) -@correction; background-repeat:repeat-y; }
	.jstree-last { background:transparent; }

	.jstree-open > .jstree-ocl { background-position:-(@image-height * 4 + @correction) -@correction; }
	.jstree-closed > .jstree-ocl { background-position:-(@image-height * 3 + @correction) -@correction; }
	.jstree-leaf > .jstree-ocl { background-position:-(@image-height * 2 + @correction) -@correction; }

	.jstree-themeicon { background-position:-(@image-height * 8 + @correction) -@correction; }

	> .jstree-no-dots {
		.jstree-node, 
		.jstree-leaf > .jstree-ocl { background:transparent; }
		.jstree-open > .jstree-ocl { background-position:-(@image-height * 1 + @correction) -@correction; }
		.jstree-closed > .jstree-ocl { background-position:-@correction -@correction; }
	}

	.jstree-disabled {
		background:transparent;
		&.jstree-hovered {
			background:transparent;
		}
		&.jstree-clicked {
			background:#efefef;
		}
	}

	.jstree-checkbox {
		background-position:-(@image-height * 5 + @correction) -@correction;
		&:hover { background-position:-(@image-height * 5 + @correction) -(@image-height * 1 + @correction); }
	}

	&.jstree-checkbox-selection .jstree-clicked, .jstree-checked {
		> .jstree-checkbox {
			background-position:-(@image-height * 7 + @correction) -@correction;
			&:hover { background-position:-(@image-height * 7 + @correction) -(@image-height * 1 + @correction); }
		}
	}
	.jstree-anchor {
		> .jstree-undetermined {
			background-position:-(@image-height * 6 + @correction) -@correction;
			&:hover {
				background-position:-(@image-height * 6 + @correction) -(@image-height * 1 + @correction);
			}
		}
	}

	> .jstree-striped { background-size:auto (@base-height * 2); }

	&.jstree-rtl {
		.jstree-node { background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAACAQMAAAB49I5GAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMOBgAAGAAJMwQHdQAAAABJRU5ErkJggg=="); background-position: 100% 1px; background-repeat:repeat-y; }
		.jstree-last { background:transparent; }
		.jstree-open > .jstree-ocl { background-position:-(@image-height * 4 + @correction) -(@image-height * 1 + @correction); }
		.jstree-closed > .jstree-ocl { background-position:-(@image-height * 3 + @correction) -(@image-height * 1 + @correction); }
		.jstree-leaf > .jstree-ocl { background-position:-(@image-height * 2 + @correction) -(@image-height * 1 + @correction); }
		> .jstree-no-dots {
			.jstree-node, 
			.jstree-leaf > .jstree-ocl { background:transparent; }
			.jstree-open > .jstree-ocl { background-position:-(@image-height * 1 + @correction) -(@image-height * 1 + @correction); }
			.jstree-closed > .jstree-ocl { background-position:-@correction -(@image-height * 1 + @correction); }
		}
	}
	.jstree-themeicon-custom { background-color:transparent; background-image:none; background-position:0 0; }

	> .jstree-container-ul .jstree-loading > .jstree-ocl { background:url("@{image-path}throbber.gif") center center no-repeat; }

	.jstree-file { background:url("@{image}") -(@image-height * 3 + @correction) -(@image-height * 2 + @correction) no-repeat; }
	.jstree-folder { background:url("@{image}") -(@image-height * 8 + @correction) -(@correction) no-repeat; }
}

.jstree-default {
	.jstree-node, 
	.jstree-icon { background-repeat:no-repeat; background-color:transparent; }
	.jstree-anchor,
	.jstree-wholerow { transition:background-color 0.15s, box-shadow 0.15s; }
	.jstree-hovered { background:@clicked-color; border-radius:2px; box-shadow:inset 0 0 1px #ccc; }
	.jstree-clicked { background:#beebff; border-radius:2px; box-shadow:inset 0 0 1px #999; }
	.jstree-no-icons .jstree-anchor > .jstree-themeicon { display:none; }
	.jstree-disabled {
		background:transparent; color:#666;
		&.jstree-hovered { background:transparent; box-shadow:none; }
		&.jstree-clicked { background:#efefef; }
		> .jstree-icon { opacity:0.8; filter: url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'jstree-grayscale\'><feColorMatrix type=\'matrix\' values=\'0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0\'/></filter></svg>#jstree-grayscale"); /* Firefox 10+ */ filter: gray; /* IE6-9 */ -webkit-filter: grayscale(100%); /* Chrome 19+ & Safari 6+ */ }
	}
	// search
	.jstree-search { font-style:italic; color:#8b0000; font-weight:bold; }
	// checkboxes
	.jstree-no-checkboxes .jstree-checkbox { display:none !important; }
	&.jstree-checkbox-no-clicked {
		.jstree-clicked {
			background:transparent;
			box-shadow:none;
			&.jstree-hovered { background:@clicked-color; }
		}
		> .jstree-wholerow-ul .jstree-wholerow-clicked {
			background:transparent;
			&.jstree-wholerow-hovered { background:@clicked-color; }
		}
	}
	// drag'n'drop
	#jstree-dnd& {
		.jstree-ok,
		.jstree-er { background-image:url("@{image-path}32px.png"); background-repeat:no-repeat; background-color:transparent; }
		i { background:transparent; width:16px; height:16px; }
		.jstree-ok { background-position: -9px -71px; }
		.jstree-er { background-position: -39px -71px; }
	}
	// stripes
	> .jstree-striped { background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAkCAMAAAB/qqA+AAAABlBMVEUAAAAAAAClZ7nPAAAAAnRSTlMNAMM9s3UAAAAXSURBVHjajcEBAQAAAIKg/H/aCQZ70AUBjAATb6YPDgAAAABJRU5ErkJggg==") left top repeat; }
	// wholerow
	> .jstree-wholerow-ul .jstree-hovered, 
	> .jstree-wholerow-ul .jstree-clicked { background:transparent; box-shadow:none; border-radius:0; }
	.jstree-wholerow { -moz-box-sizing:border-box; -webkit-box-sizing:border-box; box-sizing:border-box; }
	.jstree-wholerow-hovered { background:@clicked-color; }
	.jstree-wholerow-clicked { .gradient(#beebff, #a8e4ff); }
}

// theme variants
.jstree-default {
	.jstree-theme(24px, "@{image-path}32px.png", 32px);
	&.jstree-rtl .jstree-node { background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAACAQMAAAB49I5GAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMOBgAAGAAJMwQHdQAAAABJRU5ErkJggg=="); }
	&.jstree-rtl .jstree-last { background:transparent; }
}
.jstree-default-small {
	.jstree-theme(18px, "@{image-path}32px.png", 32px);
	&.jstree-rtl .jstree-node { background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAACAQMAAABv1h6PAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjAAMHBgAAiABBI4gz9AAAAABJRU5ErkJggg=="); }
	&.jstree-rtl .jstree-last { background:transparent; }
}
.jstree-default-large {
	.jstree-theme(32px, "@{image-path}32px.png", 32px);
	&.jstree-rtl .jstree-node { background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAACAQMAAAAD0EyKAAAABlBMVEUAAAAdHRvEkCwcAAAAAXRSTlMAQObYZgAAAAxJREFUCNdjgIIGBgABCgCBvVLXcAAAAABJRU5ErkJggg=="); }
	&.jstree-rtl .jstree-last { background:transparent; }
}

// mobile theme attempt
@media (max-width: 768px) {
	@base-height: 40px;
	#jstree-dnd.jstree-dnd-responsive {
		line-height:@base-height; font-weight:bold; font-size:1.1em; text-shadow:1px 1px white;
		> i { background:transparent; width:@base-height; height:@base-height; }
		> .jstree-ok { background-image:url("@{image-path}@{base-height}.png"); background-position:0 -(@base-height * 5); background-size:(@base-height * 3) (@base-height * 6); }
		> .jstree-er { background-image:url("@{image-path}@{base-height}.png"); background-position:-(@base-height * 1) -(@base-height * 5); background-size:(@base-height * 3) (@base-height * 6); }
	}
	#jstree-marker.jstree-dnd-responsive {
		border-left-width:10px;
		border-top-width:10px;
		border-bottom-width:10px;
		margin-top:-10px;
	}
}
.jstree-default-responsive {
	@base-height: 40px;
	@media (max-width: 768px) {
		// background image
		.jstree-icon { background-image:url("@{image-path}@{base-height}.png"); }

		.jstree-node, 
		.jstree-leaf > .jstree-ocl { background:transparent; }

		.jstree-node { min-height:@base-height; line-height:@base-height; margin-left:@base-height; min-width:@base-height; white-space:nowrap; }
		.jstree-anchor { line-height:@base-height; height:@base-height; }
		.jstree-icon, .jstree-icon:empty { width:@base-height; height:@base-height; line-height:@base-height; }

		> .jstree-container-ul > .jstree-node { margin-left:0; }
		&.jstree-rtl .jstree-node { margin-left:0; margin-right:@base-height; }
		&.jstree-rtl .jstree-container-ul > .jstree-node { margin-right:0; }

		.jstree-ocl,
		.jstree-themeicon,
		.jstree-checkbox { background-size:(@base-height * 3) (@base-height * 6); }
		.jstree-leaf > .jstree-ocl { background:transparent; }
		.jstree-open > .jstree-ocl { background-position:0 0px !important; }
		.jstree-closed > .jstree-ocl { background-position:0 -(@base-height * 1) !important; }
		&.jstree-rtl .jstree-closed > .jstree-ocl { background-position:-(@base-height * 1) 0px !important; }

		.jstree-themeicon { background-position:-(@base-height * 1) -(@base-height * 1); }

		.jstree-checkbox, .jstree-checkbox:hover { background-position:-(@base-height * 1) -(@base-height * 2); }
		&.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox,
		&.jstree-checkbox-selection .jstree-clicked > .jstree-checkbox:hover,
		.jstree-checked > .jstree-checkbox,
		.jstree-checked > .jstree-checkbox:hover { background-position:0 -(@base-height * 2); }
		.jstree-anchor > .jstree-undetermined, .jstree-anchor > .jstree-undetermined:hover { background-position:0 -(@base-height * 3); }

		.jstree-anchor { font-weight:bold; font-size:1.1em; text-shadow:1px 1px white; }

		> .jstree-striped { background:transparent; }
		.jstree-wholerow { border-top:1px solid rgba(255,255,255,0.7); border-bottom:1px solid rgba(64,64,64,0.2); background:#ebebeb; height:@base-height; }
		.jstree-wholerow-hovered { background:@clicked-color; }
		.jstree-wholerow-clicked { background:#beebff; }

		// thanks to PHOTONUI
		.jstree-children .jstree-last > .jstree-wholerow { box-shadow: inset 0 -6px 3px -5px #666; }
		.jstree-children .jstree-open > .jstree-wholerow { box-shadow: inset 0 6px 3px -5px #666; border-top:0; }
		.jstree-children .jstree-open + .jstree-open { box-shadow:none; }

		// experiment
		.jstree-node,
		.jstree-icon,
		.jstree-node > .jstree-ocl,
		.jstree-themeicon,
		.jstree-checkbox { background-image:url("@{image-path}@{base-height}.png"); background-size:(@base-height * 3) (@base-height * 6); }

		.jstree-node { background-position:-(@base-height * 2) 0; background-repeat:repeat-y; }
		.jstree-last { background:transparent; }
		.jstree-leaf > .jstree-ocl { background-position:-(@base-height * 1) -(@base-height * 3); }
		.jstree-last > .jstree-ocl { background-position:-(@base-height * 1) -(@base-height * 4); }
		/*
		.jstree-open > .jstree-ocl,
		.jstree-closed > .jstree-ocl { border-radius:20px; background-color:white; }
		*/

		.jstree-themeicon-custom { background-color:transparent; background-image:none; background-position:0 0; }
		.jstree-file { background:url("@{image-path}@{base-height}.png") 0 -(@base-height * 4) no-repeat; background-size:(@base-height * 3) (@base-height * 6); }
		.jstree-folder { background:url("@{image-path}@{base-height}.png") -(@base-height * 1) -(@base-height * 1) no-repeat; background-size:(@base-height * 3) (@base-height * 6); }
	}
}

.jstree-default > .jstree-container-ul > .jstree-node { margin-left:0; margin-right:0; }