/* TODO - dopsat z<PERSON>ladní p<PERSON> a k nim odpo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> */
.icon-file-doc:before {
	content: '\e0db';
}
.icon-file-docx:before {
	content: '\e0db';
}

input.loading {
	background: url('../img/ico/tree/throbber.gif') 95% 50% no-repeat;
}

label.waiting,
strong.waiting {
	color: #ce5dff;
}
label.progress,
strong.progress {
	color: #ff9800;
}
label.shipped,
strong.shipped {
	color: #0052cc;
}
label.ready,
strong.ready {
	color: #5597c7;
}
label.partially,
strong.partially {
	color: #106508;
}
label.done,
strong.done {
	color: #33cc33;
}
label.cancel,
strong.cancel {
	color: #cc0000;
}

#tab-products ul.sortable .inner {
	min-height: 16px;
}

body.page-login {
	max-width: 100%;
}
.page-login #main {
	border: none;
}

.jstree-checkbox:before {
	content: '' !important;
}

.jstree .nopublic > ins {
	color: grey;
}

.grid-row {
	font-size: 0px;
}
.grid-row > * {
	font-size: 14px;
}

.jstree li:before {
	display: none;
}

hr {
	height: 1px;
	background: #ddd;
	border: none;
	margin: 20px 0;
}

.pink {
	color: #dca6af;
}
.blue {
	color: #457ba2;
}

.inp-fix-suggest {
	overflow: visible;
}
.inp-fix-suggest .suggest {
	position: absolute;
	left: 0;
	right: 0;
	top: 100%;
	display: none;
	z-index: 10;
	padding-bottom: 1px;
}
.inp-fix-suggest .suggest ul {
	border: 2px solid #ddd;
	margin-bottom: 60px;
	max-height: 500px;
	overflow: hidden;
	overflow-y: auto;
	background: #fff;
}
.inp-fix-suggest .suggest li {
	padding: 5px 15px;
	border-top: 1px solid #ddd;
	cursor: pointer;
}
.inp-fix-suggest .suggest li:first-child {
	border-top: 0;
}
.inp-fix-suggest .suggest li.selected,
.inp-fix-suggest .suggest li:hover {
	background: #f9f9f9;
}

.inp-fix-suggest .suggest a {
	color: #4790d2 !important;
}

.c-templates__list {
	display: -webkit-flex;
	display: -moz-flex;
	display: -ms-flex;
	display: -o-flex;
	display: flex;
	overflow: hidden;
	overflow-x: auto;
	margin: 0 0 -10px -10px;
}
.c-templates__item {
	position: relative;
	border: 10px solid transparent;
	border-width: 0 0 10px 10px;
	width: 300px;
}
.c-templates input {
	position: absolute;
	left: 0;
	top: 0.25em;
}
.c-templates strong {
	display: block;
	margin-bottom: 10px;
	padding-left: 25px;
}
.c-templates input:checked ~ img {
	border-color: #000;
}
.c-templates img {
	border: 1px solid #eee;
}

#main {
	max-width: none;
}

.tab-fragment {
	max-width: 1000px;
}

.icon svg {
	width: 36px;
	height: 36px;
}

.skbox-window {
	z-index: 1500;
}

#js-media-library-trigger {
	display: none;
}

.crossroad-images li:hover .thumb {
	cursor: grab;
}

.crossroad-images li.is-grabbing {
	pointer-events: none;
	opacity: 0.3;
}

/*message*/
.message {
	/* display: inline-block;
	margin-top: 0; */
	padding: 8px 20px;
	margin: 1em 0 1.4em;
	background: #f3f3f3;
}
.message li {
	margin: 0;
}
.message ul li:before {
	background: #666;
}
.message ol li:before {
	color: #666;
}
.message p,
.message ul,
.message ol {
	margin-bottom: 0.5em;
}
.message :last-child {
	margin-bottom: 0;
}
.message-ok {
	background: #3c3;
	color: #fff;
}
.message-ok ul li:before {
	background: #fff;
}
.message-ok ol li:before {
	color: #fff;
}
.message-error {
	background: #c00;
	color: #fff;
}
.message-error ul li:before {
	background: #fff;
}
.message-error ol li:before {
	color: #fff;
}
.message-info {
	background: #fc3;
	color: #333;
}
.message-info ul li:before {
	background: #333;
}
.message-info ol li:before {
	color: #333;
}

/* spacing */
.mb-15 {
	margin-bottom: 15px;
}

#search {
	position: relative;
	float: right;
	margin: 0;
	padding: 0 50px 0 0;
	height: 50px;
	margin-right: 15px;
	background: rgba(0, 0, 0, 0.1);
}
#search .search-trigger {
	width: 50px;
	height: 50px;
	position: absolute;
	top: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.2);
	text-align: center;
	cursor: pointer;
}
#search .search-trigger:hover {
	background: rgba(0, 0, 0, 0.3);
}
#search .icon {
	font-size: 20px;
	line-height: 48px;
}
#search .inp-fix {
	padding: 7px 0;
	width: 0;
	transition: width 0.3s ease-out;
}
#search.is-opened .inp-fix {
	width: 50vw;
}
#search .inp-text {
	background: transparent;
	border-color: transparent;
	color: #fff;
}

html {
	overflow: auto;
}
body {
	overflow: visible;
}
table {
	clear: none;
}

/* NEW SKIN */
html {
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
}
#header {
	background: #000;
	/* background-color: red; */
}
#logo {
	color: #3cdbc0;
	font-weight: normal;
}
#logo .icon {
	width: 20px;
	display: inline-block;
	vertical-align: middle;
}
#logo .icon svg {
	width: 20px !important;
	height: 20px !important;
}
#search .search-trigger,
#user .logout {
	background: rgba(255, 255, 255, 0.3);
}
.menu-main a.sub {
	padding-left: 70px;
}
.menu-main a.sub .icon {
	left: 45px;
}
.grid-row {
	display: flex;
	flex-wrap: wrap;
}
body:before {
	width: 220px;
}
.col-side,
.menu-hover .col-side {
	width: 180px;
}
.col-content {
	margin-left: 300px;
}
body.page-login {
	background: #000;
}
